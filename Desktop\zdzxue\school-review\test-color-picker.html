<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>颜色选择器修复测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@simonwep/pickr/dist/themes/classic.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/review.css">
    <style>
        body {
            padding: 20px;
            background: var(--light-gray);
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            background: #f9f9f9;
        }
        .test-button {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            background: var(--hover-color);
            transform: translateY(-2px);
        }
        .status {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            font-weight: 500;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        
        .demo-comment-box {
            background: #ffffff;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            border-left: 5px solid var(--primary-color);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .demo-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: var(--text-color);
        }
        
        .demo-content {
            color: var(--text-color);
            line-height: 1.6;
        }
        
        .color-info {
            margin-top: 10px;
            font-size: 0.9rem;
            color: #666;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎨 颜色选择器修复测试</h1>
        
        <div class="test-section">
            <h3>1. 颜色选择器位置测试</h3>
            <p>测试颜色选择器是否在屏幕中央正确显示</p>
            <button class="test-button" onclick="testColorPickerPosition()">打开颜色选择器</button>
            <div id="positionStatus" class="status info">点击上方按钮测试颜色选择器位置</div>
        </div>
        
        <div class="test-section">
            <h3>2. 颜色预览测试</h3>
            <p>测试选择颜色后预览框和按钮的颜色变化</p>
            <button class="test-button" onclick="testColorPreview()">测试颜色预览</button>
            <div id="previewStatus" class="status info">点击上方按钮测试颜色预览功能</div>
            
            <!-- 模拟评论框 -->
            <div class="demo-comment-box" id="demoCommentBox">
                <div class="demo-title">这是一个示例评论标题</div>
                <div class="demo-content">这是评论内容，用来展示选择的颜色效果。您可以通过颜色选择器来改变这个评论框的背景颜色。</div>
                <div class="color-info" id="colorInfo">当前颜色: #ffffff</div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>3. 完整功能测试</h3>
            <p>测试完整的评论编辑器颜色选择功能</p>
            <button class="test-button" onclick="openFullEditor()">打开评论编辑器</button>
            <div id="fullTestStatus" class="status info">点击上方按钮打开完整的评论编辑器</div>
        </div>
    </div>

    <!-- 简化的颜色选择器 -->
    <div class="color-picker-container" id="testColorPickerContainer" style="display: none;">
        <div class="color-picker-title">
            <span>选择评论框颜色</span>
            <button type="button" class="color-picker-close" onclick="closeTestColorPicker()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="color-picker" id="testColorPicker"></div>
        <div class="color-preview-section">
            <div class="color-preview-title">预览效果</div>
            <div class="color-preview-box" id="testColorPreviewBox">
                <div class="color-preview-text">这是您的评论框颜色预览</div>
            </div>
        </div>
    </div>

    <!-- 完整评论编辑器弹窗 -->
    <div class="modal-overlay" id="testModalOverlay">
        <div class="comment-modal">
            <div class="modal-header">
                <h3 class="modal-title">发表评论 - 颜色测试</h3>
                <button class="modal-close" onclick="closeFullEditor()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form class="comment-form">
                    <div class="form-group">
                        <label class="form-label">标题</label>
                        <input type="text" class="form-input" placeholder="请输入评论标题..." value="测试评论标题">
                    </div>
                    <div class="form-group">
                        <label class="form-label">内容</label>
                        <textarea class="form-textarea" placeholder="分享你的校园生活...">这是一个测试评论内容，用来展示颜色选择功能。</textarea>
                    </div>
                    <div class="form-actions">
                        <div class="action-buttons">
                            <button type="button" class="action-btn" id="testColorPickerBtn" onclick="toggleTestColorPicker()">
                                <i class="fas fa-palette"></i>
                                <span>选择颜色</span>
                            </button>
                        </div>
                    </div>
                    <div class="form-submit">
                        <button type="button" class="cancel-btn" onclick="closeFullEditor()">取消</button>
                        <button type="button" class="submit-btn">发表评论</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/@simonwep/pickr/dist/pickr.min.js"></script>
    <script>
        let testColorPicker = null;
        let currentColor = '#ffffff';
        
        // 初始化测试颜色选择器
        function initTestColorPicker() {
            if (typeof Pickr !== 'undefined') {
                testColorPicker = Pickr.create({
                    el: '#testColorPicker',
                    theme: 'classic',
                    default: '#ffffff',
                    swatches: [
                        '#ffffff', '#f8f9fa', '#e9ecef', '#dee2e6',
                        '#ffe8e8', '#fff0e8', '#fff8e8', '#f0ffe8',
                        '#e8f8ff', '#e8f0ff', '#f0e8ff', '#ffe8f8',
                        '#5e35b1', '#7e57c2', '#9c27b0', '#e91e63',
                        '#f44336', '#ff9800', '#4caf50', '#2196f3'
                    ],
                    components: {
                        preview: true,
                        opacity: false,
                        hue: true,
                        interaction: {
                            hex: true,
                            rgba: false,
                            input: true,
                            save: true
                        }
                    }
                });
                
                // 监听颜色变化
                testColorPicker.on('change', (color) => {
                    if (color) {
                        const colorString = color.toHEXA().toString();
                        updateTestColorPreview(colorString);
                    }
                });
                
                // 监听保存
                testColorPicker.on('save', (color) => {
                    if (color) {
                        currentColor = color.toHEXA().toString();
                        updateTestColorPreview(currentColor);
                        updateTestColorButton();
                        updateDemoCommentBox(currentColor);
                        showTestMessage('颜色已选择: ' + currentColor, 'success');
                    }
                    closeTestColorPicker();
                });
                
                // 监听取消
                testColorPicker.on('cancel', () => {
                    closeTestColorPicker();
                });
            }
        }
        
        // 测试颜色选择器位置
        function testColorPickerPosition() {
            const container = document.getElementById('testColorPickerContainer');
            const status = document.getElementById('positionStatus');
            
            if (container) {
                container.style.display = 'block';
                document.body.style.overflow = 'hidden';
                
                if (testColorPicker) {
                    testColorPicker.show();
                }
                
                status.className = 'status success';
                status.innerHTML = '✅ 颜色选择器已在屏幕中央显示<br>📝 请检查位置是否正确，然后点击关闭按钮';
            } else {
                status.className = 'status error';
                status.innerHTML = '❌ 颜色选择器容器未找到';
            }
        }
        
        // 关闭测试颜色选择器
        function closeTestColorPicker() {
            const container = document.getElementById('testColorPickerContainer');
            if (container) {
                container.style.display = 'none';
                document.body.style.overflow = '';
                
                if (testColorPicker) {
                    testColorPicker.hide();
                }
            }
        }
        
        // 切换测试颜色选择器
        function toggleTestColorPicker() {
            const container = document.getElementById('testColorPickerContainer');
            const isVisible = container.style.display === 'block';
            
            if (isVisible) {
                closeTestColorPicker();
            } else {
                testColorPickerPosition();
            }
        }
        
        // 更新测试颜色预览
        function updateTestColorPreview(color) {
            const previewBox = document.getElementById('testColorPreviewBox');
            if (previewBox) {
                previewBox.style.background = color;
                
                // 调整文字颜色
                const textColor = getContrastColor(color);
                const textElement = previewBox.querySelector('.color-preview-text');
                if (textElement) {
                    textElement.style.color = textColor;
                    textElement.style.textShadow = textColor === '#000000' ? 
                        '1px 1px 2px rgba(255, 255, 255, 0.8)' : 
                        '1px 1px 2px rgba(0, 0, 0, 0.8)';
                }
            }
        }
        
        // 更新测试颜色按钮
        function updateTestColorButton() {
            const btn = document.getElementById('testColorPickerBtn');
            if (btn) {
                btn.style.background = `linear-gradient(45deg, ${currentColor}, ${currentColor}dd)`;
                btn.style.borderColor = currentColor;
            }
        }
        
        // 更新演示评论框
        function updateDemoCommentBox(color) {
            const box = document.getElementById('demoCommentBox');
            const colorInfo = document.getElementById('colorInfo');
            
            if (box) {
                box.style.background = color;
                box.style.borderLeftColor = color;
                
                // 调整文字颜色
                const textColor = getContrastColor(color);
                box.style.color = textColor;
            }
            
            if (colorInfo) {
                colorInfo.textContent = `当前颜色: ${color}`;
            }
        }
        
        // 获取对比色
        function getContrastColor(hexColor) {
            const hex = hexColor.replace('#', '');
            const r = parseInt(hex.substr(0, 2), 16);
            const g = parseInt(hex.substr(2, 2), 16);
            const b = parseInt(hex.substr(4, 2), 16);
            const brightness = (r * 299 + g * 587 + b * 114) / 1000;
            return brightness > 128 ? '#000000' : '#ffffff';
        }
        
        // 测试颜色预览
        function testColorPreview() {
            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7', '#dda0dd'];
            let index = 0;
            
            const status = document.getElementById('previewStatus');
            status.className = 'status info';
            status.innerHTML = '🎨 正在测试颜色预览功能...';
            
            const interval = setInterval(() => {
                const color = colors[index];
                updateTestColorPreview(color);
                updateDemoCommentBox(color);
                currentColor = color;
                updateTestColorButton();
                
                index++;
                if (index >= colors.length) {
                    clearInterval(interval);
                    status.className = 'status success';
                    status.innerHTML = '✅ 颜色预览功能测试完成！<br>📝 预览框和演示评论框的颜色应该已经改变';
                }
            }, 800);
        }
        
        // 打开完整编辑器
        function openFullEditor() {
            const modal = document.getElementById('testModalOverlay');
            if (modal) {
                modal.classList.add('active');
                document.body.style.overflow = 'hidden';
                
                const status = document.getElementById('fullTestStatus');
                status.className = 'status success';
                status.innerHTML = '✅ 完整评论编辑器已打开<br>📝 请点击"选择颜色"按钮测试颜色选择功能';
            }
        }
        
        // 关闭完整编辑器
        function closeFullEditor() {
            const modal = document.getElementById('testModalOverlay');
            if (modal) {
                modal.classList.remove('active');
                document.body.style.overflow = '';
            }
            closeTestColorPicker();
        }
        
        // 显示测试消息
        function showTestMessage(message, type) {
            // 创建临时消息
            const toast = document.createElement('div');
            toast.className = `toast-notification ${type}`;
            toast.textContent = message;
            toast.style.cssText = `
                position: fixed;
                top: 100px;
                left: 50%;
                transform: translateX(-50%);
                background: ${type === 'success' ? 'rgba(39, 174, 96, 0.9)' : 'rgba(52, 152, 219, 0.9)'};
                color: white;
                padding: 12px 24px;
                border-radius: 25px;
                font-size: 0.95rem;
                font-weight: 500;
                z-index: 3000;
                opacity: 0;
                transition: all 0.3s ease;
                pointer-events: none;
                backdrop-filter: blur(10px);
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            `;
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translateX(-50%) translateY(10px)';
            }, 100);
            
            setTimeout(() => {
                toast.style.opacity = '0';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, 2000);
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initTestColorPicker();
            updateTestColorPreview(currentColor);
            updateTestColorButton();
            updateDemoCommentBox(currentColor);
            
            // 点击遮罩关闭
            document.getElementById('testModalOverlay').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeFullEditor();
                }
            });
            
            console.log('颜色选择器测试页面初始化完成');
        });
    </script>
</body>
</html>
