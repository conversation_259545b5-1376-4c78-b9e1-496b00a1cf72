# 🚀 Vercel 部署指南

## 📋 部署前准备

### 1. 环境变量配置
在 Vercel 项目设置中添加以下环境变量：

```bash
# 123云盘图床配置
CLOUD123_ACCESS_TOKEN=your_access_token_here
CLOUD123_PARENT_FILE_ID=your_parent_file_id_here

# Vercel KV数据库配置（可选）
KV_REST_API_URL=your_kv_rest_api_url
KV_REST_API_TOKEN=your_kv_rest_api_token

# 环境标识
NODE_ENV=production
```

### 2. 项目结构确认
确保项目包含以下核心文件：
- `index.html` - 主页
- `school-review/index.html` - 校评页面
- `admin/index.html` - 后台管理页面
- `voting/index.html` - 投票页面
- `vercel.json` - Vercel配置文件

## 🔧 Vercel 配置说明

### 路由配置
- **主页**: `/` → `/index.html`
- **校评页面**: `/school-review` → `/school-review/index.html`
- **后台管理**: `/admin` → `/admin/index.html`
- **投票页面**: `/voting` → `/voting/index.html`

### 缓存策略
- **静态资源** (CSS/JS/图片): 1年缓存，不可变
- **HTML文件**: 无缓存，每次重新验证
- **字体文件**: 1年缓存，不可变

### 安全头部
- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: SAMEORIGIN`
- `X-XSS-Protection: 1; mode=block`
- `Referrer-Policy: strict-origin-when-cross-origin`
- `Permissions-Policy: camera=(), microphone=(), geolocation=()`

## 📦 部署步骤

### 方法一：GitHub 集成部署
1. 将代码推送到 GitHub 仓库
2. 在 Vercel 中导入 GitHub 仓库
3. 配置环境变量
4. 点击部署

### 方法二：Vercel CLI 部署
```bash
# 安装 Vercel CLI
npm i -g vercel

# 登录 Vercel
vercel login

# 部署项目
vercel

# 生产环境部署
vercel --prod
```

## 🔍 部署验证

### 功能测试清单
- [ ] 主页正常加载
- [ ] 导航栏功能正常
- [ ] 校评页面可访问
- [ ] 后台管理页面可访问（需管理员权限）
- [ ] 投票页面可访问
- [ ] 登录功能正常
- [ ] 图片上传功能正常（需配置123云盘）
- [ ] 响应式设计在移动端正常

### 性能检查
- [ ] 页面加载速度 < 3秒
- [ ] 静态资源正确缓存
- [ ] 图片优化加载
- [ ] CSS/JS 文件正确压缩

## 🛠️ 常见问题解决

### 1. 页面404错误
**问题**: 访问子页面时出现404
**解决**: 检查 `vercel.json` 中的路由配置是否正确

### 2. 静态资源加载失败
**问题**: CSS/JS文件无法加载
**解决**: 确保文件路径正确，检查大小写敏感性

### 3. 环境变量未生效
**问题**: 123云盘API调用失败
**解决**: 
- 检查 Vercel 项目设置中的环境变量配置
- 确保变量名称与代码中使用的一致
- 重新部署项目使环境变量生效

### 4. 图片上传功能异常
**问题**: 图片无法上传到123云盘
**解决**:
- 验证 `CLOUD123_ACCESS_TOKEN` 是否有效
- 检查 `CLOUD123_PARENT_FILE_ID` 是否正确
- 确认123云盘API配额是否充足

## 📊 监控和维护

### 性能监控
- 使用 Vercel Analytics 监控页面性能
- 定期检查 Core Web Vitals 指标
- 监控API调用成功率

### 日志查看
```bash
# 查看部署日志
vercel logs

# 查看函数日志
vercel logs --follow
```

### 更新部署
```bash
# 重新部署
vercel --prod

# 回滚到上一版本
vercel rollback
```

## 🔗 相关链接

- [Vercel 官方文档](https://vercel.com/docs)
- [123云盘开放平台](https://www.123pan.com/open)
- [项目 GitHub 仓库](#) <!-- 替换为实际仓库地址 -->

## 📞 技术支持

如遇到部署问题，请检查：
1. `vercel.json` 配置是否正确
2. 环境变量是否完整配置
3. 项目文件结构是否完整
4. 网络连接是否正常

---

**注意**: 首次部署后，建议进行全面的功能测试，确保所有页面和功能正常工作。
