你现在是一位前端开发工程师，请根据以下需求进行代码编写
我想在想为学校建造一个网站以下是设计思路
我将上传到github并通过vercel进行部署，请注意符合vercel要求
1.颜色：主要以紫色与白色为主色，搭配浅灰或低饱和度的辅助色（如浅蓝、米白）平衡视觉冲击力
2.风格：简洁，美观；采用视差滚动效果，滑动时图片与文字分层移动，增强沉浸感；
3.设计语言：JavaScript,css,html等简单语言结构，不要使用复杂框架
4.页面设计：
    首页：
    1.顶部导航栏：最左上角为校徽logo右边带上学校名字“重庆市梁平区知德中学”，最右边为登录按钮往左依次是用户头像，其他页面导航
    2.导航栏下方为欢迎语。如6：00-11：00显示“上午好，同学”，11：00-13：00显示“中午好，同学”，13：00-20：00显示“下午好同学”20：00-24：00显示“晚上好同学”0：00-6：00显示“天黑了，多休息，同学（所有文字加粗（醒目）并添加类似打字机的特效逐个快速显示，在用户进入网站后显示。
    3.欢迎语下为学校简介
    5.简介下方图片轮播，以重点强调重大新闻，点击图片后跳转到详细页面（某个网址）
    6.下方加入新闻动态：设置轮播图展示学校活动、荣誉奖项，支持按标签分类（如“教学成果”“学生风采”）。
    7.在页脚集成微信等图标，支持内容分享与互动。
    后台管理：
    1.按钮在控制在导航栏中央，只有管理员才显示该按钮和进入该页面。
    2.在用户列表中添加搜索框，可以通过搜索用户名对用户进行管理
    3.头衔功能：管理员可以给其他用户添加一个头衔，可以自定义头衔颜色，用户在发送评论或回复时在其名字旁显示，用户也可以自由选择是否带头衔发布。
    4.添加禁言功能，被禁言后的用户在评论页的添加按钮显示灰色，点击提示“您被管理员禁言，请联系管理员”（回复功能也不能使用），但可以点赞或“踩”
    校评：
    1.在导航栏添加一个“校评”按钮，点击后跳转到新页面，该页面为评论页面（只允许登录后的用户添加评论）
    2.添加评论功能，样式为长方形框，右下角有加号按钮，点击显示弹窗可以添加评论，有标题，内容等，内容下方有个添加图片的按钮，用户可以从相册中上传照片带着评论内容。
    3.注意图片与评论内容的绑定，所有上传的图片保存在123图床的imags文件夹中。
    4.发送的评论框可以自定义颜色。在上传图片的按钮旁添加自定义颜色的按钮，点击后有个小的颜色轮盘，用户可以通过颜色轮盘调节评论框颜色（让添加消息的弹窗周围跟随用户选择颜色变化而变化，以方便用户预览评论框的颜色变化）。
    5.每条评论右下角添加点赞按钮和“踩”按钮（注意统计点赞或“踩”的数据在旁边显示）记录用户点没点赞，即不要让用户重复点击和重复统计数据。
    6.点击评论后会向下展开评论的回复（注意展开动画，下方的其他评论会随着展开动画向下移动）。
    7.点击一级评论后会在下方显示一个评论框，点击后编写回复内容，不可上传图片。
    8.若该条评论已有回复点击后会优先加载点赞最多的五条评论，其次是最新时间发布的，若用户想看更多的回复，则用户可以点击最后一条回复下的“展开更多回复”继续加载五条回复
    9.给回复的内容也添加点赞和“踩”的功能
    10.给所有的评论添加删除按钮（位置在每个评论框的右上角，包括回复），按钮只允许发布者自己或管理员才被看见和使用。
    11.在评论页最上方添加一个搜索框，用户可以通过搜索评论中的标题或内容关键词进行快速查看评论。在搜索框旁添加排序按钮，可以选择按时间（从新到旧，从上往下），按点赞数量（从多到少，从上到下）进行排列评论
    12.注意保存用户评论的草稿，防止用户因手误关闭评论的弹窗，而丢失编辑的内容
    13.注意限制一次性加载评论的数量为10条（回复的一次性加载我在上面已提出要求为5个），防止同时加载导致服务器卡顿，当用户滑到底部最后一条评论时继续加载10个
    14.添加回到顶部的功能按钮，当用户下滑超过5条评论后就显示，否则不显示。
    投票：
    1.新建一个投票页面，并在导航栏上添加图片按钮
    2.页面保持其他页面设计风格，并保持档案栏功能一致
    3.在页面右上方添加一个新建图片按钮，点击后显示新建投票信息的悬浮窗
    4.悬浮窗中提供各个自定义功能，如投票名称，投票选项名称一，投票选项名称二，截止日期（用户可以自行开关，关闭则无限制）.默认显示两个投票选项名称若要更多选项点及下方新建，可继续自定义更多选项名称.点击悬浮窗，右下角的完成后，该页面上会显示投票框.
    5.只允许已登录用户投票，每位用户每个投票只允许投票一次，所有用户均可发起投票，但一天最多发起两个投票
    6.用户可以删除自己发起的投票，管理员可以删除任何人的投票，点击删除按钮后有二次确认提醒
5.文件布局：
网站图片统一放到images文件夹中，文件按照网页功能单独放在一个文件夹中，如
网站的首页文件应该这样放置：
/comments/
comments.html
comments.css
...
6.登录页面：用户点击登录按钮后弹出弹窗，用户可填写名字和密码，若未注册提示未注册是否注册新账号。保持整体风格
7.个人信息界面：用户点击头像后弹出个人信息界面，用户可以在修改名称和头像;保留唯一身份标识（使用一种安全手段为每个用户添加标识）
8.数据储存:图片使用123图床存储，注意分类，创建新的文件夹。客户信息等其他数据，用visa的kv数据库储存所有apikey单等都通过vercel的环境变量来调用，单独存放在.env文件中