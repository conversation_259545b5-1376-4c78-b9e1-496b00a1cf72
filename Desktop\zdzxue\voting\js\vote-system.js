/**
 * 投票系统核心功能
 * 处理投票的创建、投票、删除等核心业务逻辑
 */

class VoteSystem {
    constructor() {
        // 投票数据存储（实际项目中应该使用Vercel KV数据库）
        this.votes = [];
        this.userVotes = {}; // 用户投票记录 {voteId: {userId: [optionIds]}}
        this.userDailyVotes = {}; // 用户每日创建投票记录 {userId: {date: count}}
        
        // 分页配置
        this.pageSize = 10;
        this.currentPage = 1;
        this.totalVotes = 0;
        
        // 筛选和排序配置
        this.currentFilter = 'all';
        this.currentSort = 'time-desc';
        
        // 存储键名
        this.storageKeys = {
            votes: 'school_votes',
            userVotes: 'school_user_votes',
            dailyVotes: 'school_daily_votes'
        };
        
        // 初始化
        this.init();
    }
    
    /**
     * 初始化投票系统
     */
    init() {
        // 从本地存储加载数据
        this.loadFromStorage();
        
        // 初始化示例数据（仅用于演示）
        if (this.votes.length === 0) {
            this.initSampleData();
        }
        
        console.log('投票系统初始化完成');
    }
    
    /**
     * 从本地存储加载数据
     */
    loadFromStorage() {
        try {
            const votesData = localStorage.getItem(this.storageKeys.votes);
            const userVotesData = localStorage.getItem(this.storageKeys.userVotes);
            const dailyVotesData = localStorage.getItem(this.storageKeys.dailyVotes);
            
            if (votesData) {
                this.votes = JSON.parse(votesData);
            }
            
            if (userVotesData) {
                this.userVotes = JSON.parse(userVotesData);
            }
            
            if (dailyVotesData) {
                this.userDailyVotes = JSON.parse(dailyVotesData);
            }
        } catch (error) {
            console.error('加载投票数据失败:', error);
            this.votes = [];
            this.userVotes = {};
            this.userDailyVotes = {};
        }
    }
    
    /**
     * 保存数据到本地存储
     */
    saveToStorage() {
        try {
            localStorage.setItem(this.storageKeys.votes, JSON.stringify(this.votes));
            localStorage.setItem(this.storageKeys.userVotes, JSON.stringify(this.userVotes));
            localStorage.setItem(this.storageKeys.dailyVotes, JSON.stringify(this.userDailyVotes));
        } catch (error) {
            console.error('保存投票数据失败:', error);
        }
    }
    
    /**
     * 初始化示例数据
     */
    initSampleData() {
        const sampleVotes = [
            {
                id: 'vote_' + Date.now() + '_1',
                title: '学校食堂菜品改进建议',
                description: '为了提升同学们的用餐体验，学校食堂计划改进菜品，请大家投票选择最希望改进的方面。',
                options: [
                    { id: 'opt_1', text: '增加更多素食选择', votes: 15 },
                    { id: 'opt_2', text: '提升菜品口味', votes: 23 },
                    { id: 'opt_3', text: '增加地方特色菜', votes: 8 },
                    { id: 'opt_4', text: '改善食材新鲜度', votes: 12 }
                ],
                createdBy: 'UID_123456', // admin用户
                createdAt: Date.now() - 2 * 24 * 60 * 60 * 1000, // 2天前
                deadline: Date.now() + 5 * 24 * 60 * 60 * 1000, // 5天后
                settings: {
                    allowMultiple: false,
                    showResults: true,
                    anonymous: false
                },
                status: 'active',
                totalVotes: 58
            },
            {
                id: 'vote_' + Date.now() + '_2',
                title: '校园文化节活动安排',
                description: '即将举办的校园文化节需要大家的参与，请投票选择你最感兴趣的活动。',
                options: [
                    { id: 'opt_5', text: '文艺汇演', votes: 32 },
                    { id: 'opt_6', text: '书画展览', votes: 18 },
                    { id: 'opt_7', text: '科技创新展', votes: 25 },
                    { id: 'opt_8', text: '体育竞技赛', votes: 21 }
                ],
                createdBy: 'UID_654321', // test用户
                createdAt: Date.now() - 1 * 24 * 60 * 60 * 1000, // 1天前
                deadline: Date.now() + 3 * 24 * 60 * 60 * 1000, // 3天后
                settings: {
                    allowMultiple: true,
                    showResults: true,
                    anonymous: false
                },
                status: 'active',
                totalVotes: 96
            }
        ];
        
        this.votes = sampleVotes;
        this.saveToStorage();
    }
    
    /**
     * 创建新投票
     * @param {Object} voteData - 投票数据
     * @returns {Object} 创建结果
     */
    createVote(voteData) {
        try {
            // 验证用户登录状态
            if (!currentUser) {
                return { success: false, message: '请先登录后再创建投票' };
            }
            
            // 检查每日创建限制
            const today = new Date().toDateString();
            const userId = currentUser.uid;
            
            if (!this.userDailyVotes[userId]) {
                this.userDailyVotes[userId] = {};
            }
            
            const todayCount = this.userDailyVotes[userId][today] || 0;
            if (todayCount >= 2) {
                return { success: false, message: '每天最多只能创建2个投票' };
            }
            
            // 验证投票数据
            const validation = this.validateVoteData(voteData);
            if (!validation.valid) {
                return { success: false, message: validation.message };
            }
            
            // 生成投票ID
            const voteId = 'vote_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            
            // 处理选项数据
            const options = voteData.options.map((option, index) => ({
                id: 'opt_' + voteId + '_' + index,
                text: option.trim(),
                votes: 0
            }));
            
            // 创建投票对象
            const newVote = {
                id: voteId,
                title: voteData.title.trim(),
                description: voteData.description ? voteData.description.trim() : '',
                options: options,
                createdBy: userId,
                createdAt: Date.now(),
                deadline: voteData.deadline || null,
                settings: {
                    allowMultiple: voteData.allowMultiple || false,
                    showResults: voteData.showResults !== false,
                    anonymous: voteData.anonymous || false
                },
                status: 'active',
                totalVotes: 0
            };
            
            // 添加到投票列表
            this.votes.unshift(newVote);
            
            // 更新用户每日创建计数
            this.userDailyVotes[userId][today] = todayCount + 1;
            
            // 保存到存储
            this.saveToStorage();
            
            return { success: true, message: '投票创建成功', vote: newVote };
            
        } catch (error) {
            console.error('创建投票失败:', error);
            return { success: false, message: '创建投票时发生错误，请重试' };
        }
    }
    
    /**
     * 验证投票数据
     * @param {Object} voteData - 投票数据
     * @returns {Object} 验证结果
     */
    validateVoteData(voteData) {
        // 检查标题
        if (!voteData.title || voteData.title.trim().length === 0) {
            return { valid: false, message: '请输入投票标题' };
        }
        
        if (voteData.title.trim().length > 100) {
            return { valid: false, message: '投票标题不能超过100个字符' };
        }
        
        // 检查描述长度
        if (voteData.description && voteData.description.trim().length > 500) {
            return { valid: false, message: '投票描述不能超过500个字符' };
        }
        
        // 检查选项
        if (!voteData.options || voteData.options.length < 2) {
            return { valid: false, message: '至少需要2个投票选项' };
        }
        
        if (voteData.options.length > 10) {
            return { valid: false, message: '投票选项不能超过10个' };
        }
        
        // 检查选项内容
        const validOptions = voteData.options.filter(option => 
            option && option.trim().length > 0 && option.trim().length <= 50
        );
        
        if (validOptions.length < 2) {
            return { valid: false, message: '至少需要2个有效的投票选项' };
        }
        
        // 检查选项重复
        const uniqueOptions = [...new Set(validOptions.map(opt => opt.trim().toLowerCase()))];
        if (uniqueOptions.length !== validOptions.length) {
            return { valid: false, message: '投票选项不能重复' };
        }
        
        // 检查截止时间
        if (voteData.deadline && voteData.deadline <= Date.now()) {
            return { valid: false, message: '截止时间必须晚于当前时间' };
        }
        
        return { valid: true };
    }
    
    /**
     * 用户投票
     * @param {string} voteId - 投票ID
     * @param {Array} optionIds - 选择的选项ID数组
     * @returns {Object} 投票结果
     */
    submitVote(voteId, optionIds) {
        try {
            // 验证用户登录状态
            if (!currentUser) {
                return { success: false, message: '请先登录后再投票' };
            }
            
            // 检查用户是否被禁言
            if (currentUser.isBanned) {
                return { success: false, message: '您已被禁言，无法参与投票' };
            }
            
            // 查找投票
            const vote = this.votes.find(v => v.id === voteId);
            if (!vote) {
                return { success: false, message: '投票不存在' };
            }
            
            // 检查投票状态
            if (vote.status !== 'active') {
                return { success: false, message: '投票已结束' };
            }
            
            // 检查截止时间
            if (vote.deadline && Date.now() > vote.deadline) {
                // 更新投票状态
                vote.status = 'ended';
                this.saveToStorage();
                return { success: false, message: '投票已过期' };
            }
            
            // 检查用户是否已投票
            const userId = currentUser.uid;
            if (!this.userVotes[voteId]) {
                this.userVotes[voteId] = {};
            }
            
            if (this.userVotes[voteId][userId]) {
                return { success: false, message: '您已经投过票了' };
            }
            
            // 验证选项
            if (!optionIds || optionIds.length === 0) {
                return { success: false, message: '请选择投票选项' };
            }
            
            // 检查多选设置
            if (!vote.settings.allowMultiple && optionIds.length > 1) {
                return { success: false, message: '此投票不允许多选' };
            }
            
            // 验证选项ID
            const validOptionIds = optionIds.filter(optionId => 
                vote.options.some(option => option.id === optionId)
            );
            
            if (validOptionIds.length === 0) {
                return { success: false, message: '选择的选项无效' };
            }
            
            // 记录用户投票
            this.userVotes[voteId][userId] = validOptionIds;
            
            // 更新选项投票数
            validOptionIds.forEach(optionId => {
                const option = vote.options.find(opt => opt.id === optionId);
                if (option) {
                    option.votes++;
                }
            });
            
            // 更新总投票数
            vote.totalVotes++;
            
            // 保存到存储
            this.saveToStorage();
            
            return { success: true, message: '投票成功', selectedOptions: validOptionIds };
            
        } catch (error) {
            console.error('投票失败:', error);
            return { success: false, message: '投票时发生错误，请重试' };
        }
    }
    
    /**
     * 删除投票
     * @param {string} voteId - 投票ID
     * @returns {Object} 删除结果
     */
    deleteVote(voteId) {
        try {
            // 验证用户登录状态
            if (!currentUser) {
                return { success: false, message: '请先登录' };
            }
            
            // 查找投票
            const voteIndex = this.votes.findIndex(v => v.id === voteId);
            if (voteIndex === -1) {
                return { success: false, message: '投票不存在' };
            }
            
            const vote = this.votes[voteIndex];
            
            // 检查删除权限
            const canDelete = currentUser.role === 'admin' || vote.createdBy === currentUser.uid;
            if (!canDelete) {
                return { success: false, message: '您没有权限删除此投票' };
            }
            
            // 删除投票
            this.votes.splice(voteIndex, 1);
            
            // 删除相关的投票记录
            if (this.userVotes[voteId]) {
                delete this.userVotes[voteId];
            }
            
            // 保存到存储
            this.saveToStorage();
            
            return { success: true, message: '投票删除成功' };
            
        } catch (error) {
            console.error('删除投票失败:', error);
            return { success: false, message: '删除投票时发生错误，请重试' };
        }
    }
    
    /**
     * 获取投票列表
     * @param {Object} options - 查询选项
     * @returns {Object} 投票列表和分页信息
     */
    getVotes(options = {}) {
        try {
            const {
                filter = 'all',
                sort = 'time-desc',
                page = 1,
                pageSize = this.pageSize,
                userId = null
            } = options;
            
            let filteredVotes = [...this.votes];
            
            // 更新过期投票状态
            this.updateExpiredVotes();
            
            // 应用筛选
            switch (filter) {
                case 'active':
                    filteredVotes = filteredVotes.filter(vote => vote.status === 'active');
                    break;
                case 'ended':
                    filteredVotes = filteredVotes.filter(vote => vote.status === 'ended');
                    break;
                case 'my':
                    if (userId) {
                        filteredVotes = filteredVotes.filter(vote => vote.createdBy === userId);
                    } else {
                        filteredVotes = [];
                    }
                    break;
                default:
                    // 'all' - 不过滤
                    break;
            }
            
            // 应用排序
            switch (sort) {
                case 'time-asc':
                    filteredVotes.sort((a, b) => a.createdAt - b.createdAt);
                    break;
                case 'votes-desc':
                    filteredVotes.sort((a, b) => b.totalVotes - a.totalVotes);
                    break;
                case 'votes-asc':
                    filteredVotes.sort((a, b) => a.totalVotes - b.totalVotes);
                    break;
                default: // 'time-desc'
                    filteredVotes.sort((a, b) => b.createdAt - a.createdAt);
                    break;
            }
            
            // 计算分页
            const totalVotes = filteredVotes.length;
            const totalPages = Math.ceil(totalVotes / pageSize);
            const startIndex = (page - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const votes = filteredVotes.slice(startIndex, endIndex);
            
            return {
                votes,
                pagination: {
                    currentPage: page,
                    totalPages,
                    totalVotes,
                    pageSize,
                    hasNext: page < totalPages,
                    hasPrev: page > 1
                }
            };
            
        } catch (error) {
            console.error('获取投票列表失败:', error);
            return {
                votes: [],
                pagination: {
                    currentPage: 1,
                    totalPages: 0,
                    totalVotes: 0,
                    pageSize: this.pageSize,
                    hasNext: false,
                    hasPrev: false
                }
            };
        }
    }
    
    /**
     * 获取投票详情
     * @param {string} voteId - 投票ID
     * @returns {Object} 投票详情
     */
    getVoteDetail(voteId) {
        try {
            const vote = this.votes.find(v => v.id === voteId);
            if (!vote) {
                return { success: false, message: '投票不存在' };
            }
            
            // 检查并更新投票状态
            if (vote.deadline && Date.now() > vote.deadline && vote.status === 'active') {
                vote.status = 'ended';
                this.saveToStorage();
            }
            
            // 获取用户投票记录
            let userVote = null;
            if (currentUser && this.userVotes[voteId] && this.userVotes[voteId][currentUser.uid]) {
                userVote = this.userVotes[voteId][currentUser.uid];
            }
            
            return {
                success: true,
                vote: { ...vote },
                userVote,
                canVote: this.canUserVote(voteId),
                canDelete: this.canUserDelete(voteId)
            };
            
        } catch (error) {
            console.error('获取投票详情失败:', error);
            return { success: false, message: '获取投票详情时发生错误' };
        }
    }
    
    /**
     * 检查用户是否可以投票
     * @param {string} voteId - 投票ID
     * @returns {boolean} 是否可以投票
     */
    canUserVote(voteId) {
        if (!currentUser || currentUser.isBanned) {
            return false;
        }
        
        const vote = this.votes.find(v => v.id === voteId);
        if (!vote || vote.status !== 'active') {
            return false;
        }
        
        if (vote.deadline && Date.now() > vote.deadline) {
            return false;
        }
        
        // 检查是否已投票
        if (this.userVotes[voteId] && this.userVotes[voteId][currentUser.uid]) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 检查用户是否可以删除投票
     * @param {string} voteId - 投票ID
     * @returns {boolean} 是否可以删除
     */
    canUserDelete(voteId) {
        if (!currentUser) {
            return false;
        }
        
        const vote = this.votes.find(v => v.id === voteId);
        if (!vote) {
            return false;
        }
        
        return currentUser.role === 'admin' || vote.createdBy === currentUser.uid;
    }
    
    /**
     * 更新过期投票状态
     */
    updateExpiredVotes() {
        let hasChanges = false;
        
        this.votes.forEach(vote => {
            if (vote.status === 'active' && vote.deadline && Date.now() > vote.deadline) {
                vote.status = 'ended';
                hasChanges = true;
            }
        });
        
        if (hasChanges) {
            this.saveToStorage();
        }
    }
    
    /**
     * 获取投票统计信息
     * @param {string} userId - 用户ID（可选）
     * @returns {Object} 统计信息
     */
    getVoteStats(userId = null) {
        try {
            this.updateExpiredVotes();
            
            const stats = {
                totalVotes: this.votes.length,
                activeVotes: this.votes.filter(v => v.status === 'active').length,
                endedVotes: this.votes.filter(v => v.status === 'ended').length,
                myVotes: 0,
                myParticipations: 0
            };
            
            if (userId) {
                stats.myVotes = this.votes.filter(v => v.createdBy === userId).length;
                
                // 计算用户参与的投票数
                stats.myParticipations = Object.keys(this.userVotes).filter(voteId => 
                    this.userVotes[voteId][userId]
                ).length;
            }
            
            return stats;
            
        } catch (error) {
            console.error('获取投票统计失败:', error);
            return {
                totalVotes: 0,
                activeVotes: 0,
                endedVotes: 0,
                myVotes: 0,
                myParticipations: 0
            };
        }
    }
    
    /**
     * 检查用户今日创建投票数量
     * @param {string} userId - 用户ID
     * @returns {number} 今日创建数量
     */
    getTodayVoteCount(userId) {
        const today = new Date().toDateString();
        return this.userDailyVotes[userId] && this.userDailyVotes[userId][today] || 0;
    }
    
    /**
     * 格式化时间显示
     * @param {number} timestamp - 时间戳
     * @returns {string} 格式化的时间字符串
     */
    formatTime(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;
        
        // 小于1分钟
        if (diff < 60000) {
            return '刚刚';
        }
        
        // 小于1小时
        if (diff < 3600000) {
            return Math.floor(diff / 60000) + '分钟前';
        }
        
        // 小于1天
        if (diff < 86400000) {
            return Math.floor(diff / 3600000) + '小时前';
        }
        
        // 小于7天
        if (diff < 604800000) {
            return Math.floor(diff / 86400000) + '天前';
        }
        
        // 超过7天显示具体日期
        return date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }
    
    /**
     * 格式化截止时间显示
     * @param {number} deadline - 截止时间戳
     * @returns {string} 格式化的截止时间字符串
     */
    formatDeadline(deadline) {
        if (!deadline) {
            return '无限制';
        }
        
        const date = new Date(deadline);
        const now = new Date();
        const diff = deadline - now;
        
        if (diff <= 0) {
            return '已截止';
        }
        
        // 小于1小时
        if (diff < 3600000) {
            return Math.floor(diff / 60000) + '分钟后截止';
        }
        
        // 小于1天
        if (diff < 86400000) {
            return Math.floor(diff / 3600000) + '小时后截止';
        }
        
        // 小于7天
        if (diff < 604800000) {
            return Math.floor(diff / 86400000) + '天后截止';
        }
        
        // 超过7天显示具体日期
        return date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        }) + ' 截止';
    }
}
