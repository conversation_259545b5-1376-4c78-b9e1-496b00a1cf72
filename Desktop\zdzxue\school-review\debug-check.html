<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>校评页面调试检查</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/review.css">
    <style>
        .debug-panel {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            z-index: 9999;
            max-width: 300px;
            max-height: 400px;
            overflow-y: auto;
        }
        .debug-item {
            margin-bottom: 5px;
            padding: 3px 0;
            border-bottom: 1px solid #333;
        }
        .debug-success { color: #4CAF50; }
        .debug-error { color: #f44336; }
        .debug-warning { color: #ff9800; }
        .debug-info { color: #2196F3; }
        .debug-toggle {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #333;
            color: white;
            border: none;
            padding: 10px;
            border-radius: 5px;
            cursor: pointer;
            z-index: 10000;
        }
    </style>
</head>
<body>
    <!-- 调试面板 -->
    <div class="debug-panel" id="debugPanel">
        <h4>🔍 调试信息</h4>
        <div id="debugContent">正在检查...</div>
    </div>
    
    <button class="debug-toggle" onclick="toggleDebugPanel()">调试</button>

    <!-- 原始页面内容 -->
    <nav class="navbar">
        <div class="container">
            <div class="nav-brand">
                <a href="../index.html" class="nav-logo">
                    <i class="fas fa-graduation-cap"></i>
                    <span>校园生活</span>
                </a>
            </div>
            <div class="nav-menu">
                <a href="../index.html" class="nav-link">首页</a>
                <div class="nav-auth">
                    <button class="login-btn" id="loginBtn">登录</button>
                    <div class="avatar-container" id="avatarContainer" style="display: none;">
                        <img src="../images/avatar.png" alt="用户头像" class="user-avatar" id="userAvatar">
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <main class="main-content">
        <section class="page-header">
            <div class="container">
                <h1 class="page-title">校园评论</h1>
                <p class="page-subtitle">分享你的校园生活，发现更多精彩</p>
            </div>
        </section>

        <section class="search-section">
            <div class="container">
                <div class="search-container">
                    <div class="search-box">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" id="searchInput" placeholder="搜索评论标题或内容..." class="search-input">
                        <button class="search-btn" id="searchBtn">搜索</button>
                    </div>
                    <div class="sort-box">
                        <label for="sortSelect" class="sort-label">排序方式：</label>
                        <select id="sortSelect" class="sort-select">
                            <option value="time-desc">时间（从新到旧）</option>
                            <option value="time-asc">时间（从旧到新）</option>
                            <option value="likes-desc">点赞数（从多到少）</option>
                            <option value="likes-asc">点赞数（从少到多）</option>
                        </select>
                    </div>
                </div>
            </div>
        </section>

        <!-- 悬浮添加评论按钮 -->
        <div class="floating-add-btn" id="floatingAddBtn">
            <i class="fas fa-plus"></i>
        </div>

        <section class="comments-section">
            <div class="container">
                <div class="comments-container" id="commentsContainer">
                    <div class="loading-indicator" id="loadingIndicator">
                        <i class="fas fa-spinner fa-spin"></i>
                        <span>加载中...</span>
                    </div>
                </div>
                <div class="load-more-container" id="loadMoreContainer" style="display: none;">
                    <button class="load-more-btn" id="loadMoreBtn">加载更多评论</button>
                </div>
            </div>
        </section>
    </main>

    <!-- 添加评论弹窗 -->
    <div class="modal-overlay" id="commentModalOverlay">
        <div class="comment-modal" id="commentModal">
            <div class="modal-header">
                <h3 class="modal-title">发表评论</h3>
                <button class="modal-close" id="commentModalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="commentForm" class="comment-form">
                    <div class="form-group">
                        <label for="commentTitle" class="form-label">标题</label>
                        <input type="text" id="commentTitle" class="form-input" placeholder="请输入评论标题..." maxlength="100" required>
                        <div class="char-count">
                            <span id="titleCharCount">0</span>/100
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="commentContent" class="form-label">内容</label>
                        <textarea id="commentContent" class="form-textarea" placeholder="分享你的校园生活..." maxlength="1000" required></textarea>
                        <div class="char-count">
                            <span id="contentCharCount">0</span>/1000
                        </div>
                    </div>
                    <div class="form-submit">
                        <button type="button" class="cancel-btn" id="cancelCommentBtn">取消</button>
                        <button type="submit" class="submit-btn" id="submitCommentBtn">发表评论</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- JavaScript文件 -->
    <script>
        // 123云盘API配置
        window.CLOUD123_ACCESS_TOKEN = '';
        window.CLOUD123_PARENT_FILE_ID = '';
    </script>
    <script src="https://cdn.jsdelivr.net/npm/@simonwep/pickr/dist/pickr.min.js"></script>
    <script src="../js/main.js"></script>
    <script src="js/image-upload.js"></script>
    <script src="js/comment-system.js"></script>
    <script src="js/review.js"></script>

    <script>
        // 调试功能
        let debugVisible = true;
        
        function toggleDebugPanel() {
            const panel = document.getElementById('debugPanel');
            debugVisible = !debugVisible;
            panel.style.display = debugVisible ? 'block' : 'none';
        }
        
        function addDebugInfo(message, type = 'info') {
            const content = document.getElementById('debugContent');
            const item = document.createElement('div');
            item.className = `debug-item debug-${type}`;
            item.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            content.appendChild(item);
            content.scrollTop = content.scrollHeight;
        }
        
        function checkElements() {
            const checks = [
                { id: 'floatingAddBtn', name: '悬浮按钮' },
                { id: 'commentModalOverlay', name: '评论弹窗' },
                { id: 'commentModal', name: '评论模态框' },
                { id: 'commentForm', name: '评论表单' },
                { id: 'commentTitle', name: '标题输入框' },
                { id: 'commentContent', name: '内容输入框' }
            ];
            
            checks.forEach(check => {
                const element = document.getElementById(check.id);
                if (element) {
                    addDebugInfo(`✅ ${check.name} 元素存在`, 'success');
                } else {
                    addDebugInfo(`❌ ${check.name} 元素缺失`, 'error');
                }
            });
        }
        
        function checkCSS() {
            const floatingBtn = document.getElementById('floatingAddBtn');
            if (floatingBtn) {
                const styles = window.getComputedStyle(floatingBtn);
                addDebugInfo(`悬浮按钮位置: ${styles.position}`, 'info');
                addDebugInfo(`悬浮按钮显示: ${styles.display}`, 'info');
                addDebugInfo(`悬浮按钮层级: ${styles.zIndex}`, 'info');
                
                if (styles.position === 'fixed' && styles.display !== 'none') {
                    addDebugInfo('✅ 悬浮按钮样式正常', 'success');
                } else {
                    addDebugInfo('❌ 悬浮按钮样式异常', 'error');
                }
            }
        }
        
        function checkJavaScript() {
            if (typeof SchoolReviewApp !== 'undefined') {
                addDebugInfo('✅ SchoolReviewApp 类已定义', 'success');
            } else {
                addDebugInfo('❌ SchoolReviewApp 类未定义', 'error');
            }
            
            if (typeof schoolReviewApp !== 'undefined' && schoolReviewApp) {
                addDebugInfo('✅ schoolReviewApp 实例已创建', 'success');
            } else {
                addDebugInfo('❌ schoolReviewApp 实例未创建', 'error');
            }
            
            if (typeof ImageUploader !== 'undefined') {
                addDebugInfo('✅ ImageUploader 类已定义', 'success');
            } else {
                addDebugInfo('❌ ImageUploader 类未定义', 'error');
            }
        }
        
        function checkEvents() {
            const floatingBtn = document.getElementById('floatingAddBtn');
            if (floatingBtn) {
                // 临时添加点击事件来测试
                floatingBtn.addEventListener('click', function() {
                    addDebugInfo('✅ 悬浮按钮点击事件触发', 'success');
                }, { once: true });
                
                addDebugInfo('📝 悬浮按钮事件监听器已添加', 'info');
            }
        }
        
        // 页面加载完成后执行检查
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                addDebugInfo('🔍 开始调试检查...', 'info');
                checkElements();
                checkCSS();
                checkJavaScript();
                checkEvents();
                addDebugInfo('✅ 调试检查完成', 'success');
            }, 1000);
        });
        
        // 监听错误
        window.addEventListener('error', function(e) {
            addDebugInfo(`❌ JavaScript错误: ${e.message}`, 'error');
            addDebugInfo(`文件: ${e.filename}:${e.lineno}`, 'error');
        });
        
        // 监听未处理的Promise拒绝
        window.addEventListener('unhandledrejection', function(e) {
            addDebugInfo(`❌ Promise错误: ${e.reason}`, 'error');
        });
    </script>
</body>
</html>
