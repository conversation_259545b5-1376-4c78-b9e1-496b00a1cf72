<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>悬浮按钮修复测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/review.css">
    <style>
        body {
            padding: 20px;
            min-height: 100vh;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            background: #f9f9f9;
        }
        .test-button {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            background: var(--hover-color);
            transform: translateY(-2px);
        }
        .status {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            font-weight: 500;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 悬浮按钮修复测试</h1>
        
        <div class="test-section">
            <h3>1. 悬浮按钮显示测试</h3>
            <p>检查右下角是否显示紫色圆形加号按钮</p>
            <button class="test-button" onclick="checkFloatingButton()">检查悬浮按钮</button>
            <div id="floatingStatus" class="status info">点击上方按钮检查悬浮按钮状态</div>
        </div>
        
        <div class="test-section">
            <h3>2. 点击事件测试</h3>
            <p>测试悬浮按钮的点击事件是否正常工作</p>
            <button class="test-button" onclick="testFloatingClick()">测试点击事件</button>
            <div id="clickStatus" class="status info">点击上方按钮测试悬浮按钮点击</div>
        </div>
        
        <div class="test-section">
            <h3>3. 模拟登录状态测试</h3>
            <p>测试不同登录状态下的悬浮按钮行为</p>
            <button class="test-button" onclick="simulateLogin()">模拟登录</button>
            <button class="test-button" onclick="simulateLogout()">模拟退出</button>
            <div id="loginStatus" class="status info">当前状态：未登录</div>
        </div>
        
        <div class="test-section">
            <h3>4. Toast提示测试</h3>
            <p>测试Toast提示功能</p>
            <button class="test-button" onclick="testToast('warning')">警告提示</button>
            <button class="test-button" onclick="testToast('success')">成功提示</button>
            <button class="test-button" onclick="testToast('info')">信息提示</button>
            <div id="toastStatus" class="status info">点击上方按钮测试Toast提示</div>
        </div>
    </div>

    <!-- 悬浮按钮 -->
    <div class="floating-add-btn" id="floatingAddBtn">
        <i class="fas fa-plus"></i>
    </div>

    <!-- 评论弹窗（简化版） -->
    <div class="modal-overlay" id="commentModalOverlay">
        <div class="comment-modal" id="commentModal">
            <div class="modal-header">
                <h3 class="modal-title">发表评论</h3>
                <button class="modal-close" id="commentModalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <p>这是评论编辑器窗口的测试版本。</p>
                <p>如果您能看到这个窗口，说明悬浮按钮功能正常！</p>
                <button class="test-button" onclick="closeModal()">关闭窗口</button>
            </div>
        </div>
    </div>

    <script>
        // 模拟登录状态
        let isLoggedIn = false;
        
        // 检查悬浮按钮
        function checkFloatingButton() {
            const floatingBtn = document.getElementById('floatingAddBtn');
            const status = document.getElementById('floatingStatus');
            
            if (floatingBtn) {
                const styles = window.getComputedStyle(floatingBtn);
                const isVisible = styles.display !== 'none' && styles.visibility !== 'hidden';
                const position = styles.position;
                const zIndex = styles.zIndex;
                
                if (isVisible && position === 'fixed') {
                    status.className = 'status success';
                    status.innerHTML = `✅ 悬浮按钮正常显示<br>
                        位置: ${position}<br>
                        层级: ${zIndex}<br>
                        可见性: ${isVisible ? '可见' : '隐藏'}`;
                } else {
                    status.className = 'status error';
                    status.innerHTML = `❌ 悬浮按钮显示异常<br>
                        位置: ${position}<br>
                        层级: ${zIndex}<br>
                        可见性: ${isVisible ? '可见' : '隐藏'}`;
                }
            } else {
                status.className = 'status error';
                status.innerHTML = '❌ 未找到悬浮按钮元素';
            }
        }
        
        // 测试悬浮按钮点击
        function testFloatingClick() {
            const floatingBtn = document.getElementById('floatingAddBtn');
            const status = document.getElementById('clickStatus');
            
            if (floatingBtn) {
                // 添加点击事件监听器
                floatingBtn.addEventListener('click', function() {
                    status.className = 'status success';
                    status.innerHTML = '✅ 悬浮按钮点击事件正常工作！';
                    
                    // 测试弹窗显示
                    if (isLoggedIn) {
                        openModal();
                    } else {
                        showToast('请先登录', 'warning');
                    }
                });
                
                status.className = 'status info';
                status.innerHTML = '📝 点击事件监听器已添加，请点击右下角的悬浮按钮测试';
            } else {
                status.className = 'status error';
                status.innerHTML = '❌ 未找到悬浮按钮，无法添加点击事件';
            }
        }
        
        // 模拟登录
        function simulateLogin() {
            isLoggedIn = true;
            const status = document.getElementById('loginStatus');
            status.className = 'status success';
            status.innerHTML = '✅ 当前状态：已登录（模拟）';
        }
        
        // 模拟退出
        function simulateLogout() {
            isLoggedIn = false;
            const status = document.getElementById('loginStatus');
            status.className = 'status warning';
            status.innerHTML = '⚠️ 当前状态：未登录（模拟）';
        }
        
        // 显示Toast提示
        function showToast(message, type = 'info') {
            // 移除已存在的toast
            const existingToast = document.querySelector('.toast-notification');
            if (existingToast) {
                existingToast.remove();
            }
            
            // 创建新的toast
            const toast = document.createElement('div');
            toast.className = `toast-notification ${type}`;
            toast.textContent = message;
            
            document.body.appendChild(toast);
            
            // 显示动画
            setTimeout(() => {
                toast.classList.add('show');
            }, 100);
            
            // 自动隐藏
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, 2000);
        }
        
        // 测试Toast
        function testToast(type) {
            const messages = {
                warning: '请先登录后再发表评论',
                success: '操作成功完成！',
                info: '这是一条信息提示'
            };
            
            showToast(messages[type], type);
            
            const status = document.getElementById('toastStatus');
            status.className = 'status success';
            status.innerHTML = `✅ 显示了${type}类型的Toast提示`;
        }
        
        // 打开模态框
        function openModal() {
            const modal = document.getElementById('commentModalOverlay');
            if (modal) {
                modal.classList.add('active');
                document.body.style.overflow = 'hidden';
            }
        }
        
        // 关闭模态框
        function closeModal() {
            const modal = document.getElementById('commentModalOverlay');
            if (modal) {
                modal.classList.remove('active');
                document.body.style.overflow = '';
            }
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加关闭按钮事件
            const closeBtn = document.getElementById('commentModalClose');
            if (closeBtn) {
                closeBtn.addEventListener('click', closeModal);
            }
            
            // 点击遮罩关闭
            const modal = document.getElementById('commentModalOverlay');
            if (modal) {
                modal.addEventListener('click', function(e) {
                    if (e.target === modal) {
                        closeModal();
                    }
                });
            }
            
            console.log('测试页面初始化完成');
        });
    </script>
</body>
</html>
