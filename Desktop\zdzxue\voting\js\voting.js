/**
 * 投票页面主要逻辑（第一部分）
 * 处理页面初始化、用户交互、弹窗管理等基础功能
 */

class VotingApp {
    constructor() {
        // 核心组件
        this.voteSystem = new VoteSystem();

        // DOM元素
        this.elements = {};

        // 状态管理
        this.state = {
            isLoggedIn: false,
            currentUser: null,
            currentFilter: 'all',
            currentSort: 'time-desc',
            currentPage: 1,
            isCreatingVote: false,
            selectedVoteId: null
        };

        // 初始化
        this.init();
    }

    /**
     * 初始化应用
     */
    init() {
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initApp());
        } else {
            this.initApp();
        }
    }

    /**
     * 初始化应用主体
     */
    initApp() {
        // 获取DOM元素
        this.initElements();

        // 检查登录状态
        this.checkLoginStatus();

        // 绑定事件监听器
        this.bindEventListeners();

        // 加载投票列表
        this.loadVotes();

        // 更新统计信息
        this.updateStats();

        console.log('投票页面初始化完成');
    }

    /**
     * 获取DOM元素
     */
    initElements() {
        // 统计元素
        this.elements.totalVotes = document.getElementById('totalVotes');
        this.elements.activeVotes = document.getElementById('activeVotes');
        this.elements.myVotes = document.getElementById('myVotes');

        // 控制元素
        this.elements.filterBtns = document.querySelectorAll('.filter-btn');
        this.elements.sortSelect = document.getElementById('sortSelect');
        this.elements.votingList = document.getElementById('votingList');
        this.elements.paginationContainer = document.getElementById('paginationContainer');

        // 悬浮按钮
        this.elements.createVoteFab = document.getElementById('createVoteFab');

        // 弹窗元素
        this.elements.createVoteModal = document.getElementById('createVoteModal');
        this.elements.voteDetailModal = document.getElementById('voteDetailModal');
        this.elements.deleteConfirmModal = document.getElementById('deleteConfirmModal');
        this.elements.loginModal = document.getElementById('loginModal');
        this.elements.profileModal = document.getElementById('profileModal');

        // 创建投票表单元素
        this.elements.voteTitle = document.getElementById('voteTitle');
        this.elements.voteDescription = document.getElementById('voteDescription');
        this.elements.voteOptions = document.getElementById('voteOptions');
        this.elements.addOptionBtn = document.getElementById('addOptionBtn');
        this.elements.enableDeadline = document.getElementById('enableDeadline');
        this.elements.deadlineSettings = document.getElementById('deadlineSettings');
        this.elements.deadlineDate = document.getElementById('deadlineDate');
        this.elements.deadlineTime = document.getElementById('deadlineTime');
        this.elements.allowMultiple = document.getElementById('allowMultiple');
        this.elements.showResults = document.getElementById('showResults');
        this.elements.anonymousVote = document.getElementById('anonymousVote');

        // 按钮元素
        this.elements.closeCreateModal = document.getElementById('closeCreateModal');
        this.elements.cancelCreateBtn = document.getElementById('cancelCreateBtn');
        this.elements.createVoteBtn = document.getElementById('createVoteBtn');
        this.elements.closeDetailModal = document.getElementById('closeDetailModal');
        this.elements.closeDeleteModal = document.getElementById('closeDeleteModal');
        this.elements.cancelDeleteBtn = document.getElementById('cancelDeleteBtn');
        this.elements.confirmDeleteBtn = document.getElementById('confirmDeleteBtn');

        // 通知元素
        this.elements.createVoteNotification = document.getElementById('createVoteNotification');

        // 字符计数元素
        this.elements.titleCharCount = document.getElementById('titleCharCount');
        this.elements.descCharCount = document.getElementById('descCharCount');

        // 用户相关元素
        this.elements.loginBtn = document.getElementById('loginBtn');
        this.elements.avatarContainer = document.getElementById('avatarContainer');
        this.elements.userAvatar = document.getElementById('userAvatar');
        this.elements.adminBtn = document.getElementById('adminBtn');
    }

    /**
     * 检查登录状态
     */
    checkLoginStatus() {
        try {
            const storedUser = localStorage.getItem('currentUser');
            if (storedUser) {
                this.state.currentUser = JSON.parse(storedUser);
                this.state.isLoggedIn = true;
                this.updateUserInterface();
            } else {
                this.state.isLoggedIn = false;
                this.updateUserInterface();
            }
        } catch (error) {
            console.error('读取登录状态失败:', error);
            this.state.isLoggedIn = false;
            this.updateUserInterface();
        }
    }

    /**
     * 更新用户界面
     */
    updateUserInterface() {
        if (this.state.isLoggedIn && this.state.currentUser) {
            // 显示用户头像，隐藏登录按钮
            if (this.elements.avatarContainer) {
                this.elements.avatarContainer.style.display = 'block';
            }
            if (this.elements.loginBtn) {
                this.elements.loginBtn.style.display = 'none';
            }
            if (this.elements.userAvatar) {
                this.elements.userAvatar.src = this.state.currentUser.avatar || '../images/avatar.png';
            }

            // 显示管理员按钮（如果是管理员）
            if (this.elements.adminBtn && this.state.currentUser.role === 'admin') {
                this.elements.adminBtn.style.display = 'block';
            }
        } else {
            // 隐藏用户头像，显示登录按钮
            if (this.elements.avatarContainer) {
                this.elements.avatarContainer.style.display = 'none';
            }
            if (this.elements.loginBtn) {
                this.elements.loginBtn.style.display = 'block';
            }
            if (this.elements.adminBtn) {
                this.elements.adminBtn.style.display = 'none';
            }
        }

        // 更新统计信息
        this.updateStats();
    }

    /**
     * 绑定事件监听器
     */
    bindEventListeners() {
        // 筛选按钮事件
        this.elements.filterBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const filter = e.target.dataset.filter;
                this.setFilter(filter);
            });
        });

        // 排序选择事件
        if (this.elements.sortSelect) {
            this.elements.sortSelect.addEventListener('change', (e) => {
                this.setSort(e.target.value);
            });
        }

        // 新建投票悬浮按钮事件
        if (this.elements.createVoteFab) {
            this.elements.createVoteFab.addEventListener('click', () => {
                this.openCreateVoteModal();
            });
        }

        // 创建投票弹窗事件
        this.bindCreateVoteModalEvents();

        // 投票详情弹窗事件
        this.bindVoteDetailModalEvents();

        // 删除确认弹窗事件
        this.bindDeleteConfirmModalEvents();

        // 登录相关事件
        this.bindLoginEvents();

        // 监听用户登录/退出事件
        window.addEventListener('userLoggedIn', (e) => {
            this.state.currentUser = e.detail.user;
            this.state.isLoggedIn = true;
            this.updateUserInterface();
            this.loadVotes(); // 重新加载投票列表
        });

        window.addEventListener('userLoggedOut', () => {
            this.state.currentUser = null;
            this.state.isLoggedIn = false;
            this.updateUserInterface();
            this.loadVotes(); // 重新加载投票列表
        });
    }

    /**
     * 绑定创建投票弹窗事件
     */
    bindCreateVoteModalEvents() {
        // 关闭弹窗事件
        if (this.elements.closeCreateModal) {
            this.elements.closeCreateModal.addEventListener('click', () => {
                this.closeCreateVoteModal();
            });
        }

        if (this.elements.cancelCreateBtn) {
            this.elements.cancelCreateBtn.addEventListener('click', () => {
                this.closeCreateVoteModal();
            });
        }

        // 创建投票按钮事件
        if (this.elements.createVoteBtn) {
            this.elements.createVoteBtn.addEventListener('click', () => {
                this.handleCreateVote();
            });
        }

        // 添加选项按钮事件
        if (this.elements.addOptionBtn) {
            this.elements.addOptionBtn.addEventListener('click', () => {
                this.addVoteOption();
            });
        }

        // 截止时间开关事件
        if (this.elements.enableDeadline) {
            this.elements.enableDeadline.addEventListener('change', (e) => {
                this.toggleDeadlineSettings(e.target.checked);
            });
        }

        // 字符计数事件
        if (this.elements.voteTitle) {
            this.elements.voteTitle.addEventListener('input', () => {
                this.updateCharCount('title');
            });
        }

        if (this.elements.voteDescription) {
            this.elements.voteDescription.addEventListener('input', () => {
                this.updateCharCount('description');
            });
        }

        // 点击弹窗外部关闭
        if (this.elements.createVoteModal) {
            this.elements.createVoteModal.addEventListener('click', (e) => {
                if (e.target === this.elements.createVoteModal) {
                    this.closeCreateVoteModal();
                }
            });
        }
    }

    /**
     * 绑定投票详情弹窗事件
     */
    bindVoteDetailModalEvents() {
        if (this.elements.closeDetailModal) {
            this.elements.closeDetailModal.addEventListener('click', () => {
                this.closeVoteDetailModal();
            });
        }

        if (this.elements.voteDetailModal) {
            this.elements.voteDetailModal.addEventListener('click', (e) => {
                if (e.target === this.elements.voteDetailModal) {
                    this.closeVoteDetailModal();
                }
            });
        }
    }

    /**
     * 绑定删除确认弹窗事件
     */
    bindDeleteConfirmModalEvents() {
        if (this.elements.closeDeleteModal) {
            this.elements.closeDeleteModal.addEventListener('click', () => {
                this.closeDeleteConfirmModal();
            });
        }

        if (this.elements.cancelDeleteBtn) {
            this.elements.cancelDeleteBtn.addEventListener('click', () => {
                this.closeDeleteConfirmModal();
            });
        }

        if (this.elements.confirmDeleteBtn) {
            this.elements.confirmDeleteBtn.addEventListener('click', () => {
                this.handleDeleteVote();
            });
        }

        if (this.elements.deleteConfirmModal) {
            this.elements.deleteConfirmModal.addEventListener('click', (e) => {
                if (e.target === this.elements.deleteConfirmModal) {
                    this.closeDeleteConfirmModal();
                }
            });
        }
    }

    /**
     * 绑定登录相关事件
     */
    bindLoginEvents() {
        // 登录按钮事件
        if (this.elements.loginBtn) {
            this.elements.loginBtn.addEventListener('click', () => {
                if (!this.state.isLoggedIn) {
                    this.openLoginModal();
                }
            });
        }

        // 用户头像点击事件
        if (this.elements.avatarContainer) {
            this.elements.avatarContainer.addEventListener('click', () => {
                if (this.state.isLoggedIn) {
                    this.openProfileModal();
                }
            });
        }
    }

    /**
     * 设置筛选条件
     * @param {string} filter - 筛选条件
     */
    setFilter(filter) {
        this.state.currentFilter = filter;
        this.state.currentPage = 1;

        // 更新按钮状态
        this.elements.filterBtns.forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.filter === filter) {
                btn.classList.add('active');
            }
        });

        // 重新加载投票列表
        this.loadVotes();
    }

    /**
     * 设置排序方式
     * @param {string} sort - 排序方式
     */
    setSort(sort) {
        this.state.currentSort = sort;
        this.state.currentPage = 1;

        // 重新加载投票列表
        this.loadVotes();
    }

    /**
     * 加载投票列表
     */
    loadVotes() {
        try {
            const options = {
                filter: this.state.currentFilter,
                sort: this.state.currentSort,
                page: this.state.currentPage,
                userId: this.state.currentUser ? this.state.currentUser.uid : null
            };

            const result = this.voteSystem.getVotes(options);

            // 渲染投票列表
            this.renderVoteList(result.votes);

            // 渲染分页
            this.renderPagination(result.pagination);

        } catch (error) {
            console.error('加载投票列表失败:', error);
            this.showError('加载投票列表失败，请刷新页面重试');
        }
    }

    /**
     * 渲染投票列表
     * @param {Array} votes - 投票数组
     */
    renderVoteList(votes) {
        if (!this.elements.votingList) return;

        if (votes.length === 0) {
            this.renderEmptyState();
            return;
        }

        const votesHtml = votes.map(vote => this.renderVoteCard(vote)).join('');
        this.elements.votingList.innerHTML = votesHtml;

        // 绑定投票卡片事件
        this.bindVoteCardEvents();
    }

    /**
     * 渲染单个投票卡片
     * @param {Object} vote - 投票对象
     * @returns {string} HTML字符串
     */
    renderVoteCard(vote) {
        const canVote = this.voteSystem.canUserVote(vote.id);
        const canDelete = this.voteSystem.canUserDelete(vote.id);
        const hasVoted = this.state.currentUser &&
                        this.voteSystem.userVotes[vote.id] &&
                        this.voteSystem.userVotes[vote.id][this.state.currentUser.uid];

        // 获取创建者信息
        const creator = this.getCreatorInfo(vote.createdBy);

        // 格式化时间
        const timeAgo = this.voteSystem.formatTime(vote.createdAt);
        const deadlineText = this.voteSystem.formatDeadline(vote.deadline);

        return `
            <div class="vote-card" data-vote-id="${vote.id}">
                <div class="vote-header">
                    <h3 class="vote-title">${this.escapeHtml(vote.title)}</h3>
                    <span class="vote-status ${vote.status === 'active' ? 'status-active' : 'status-ended'}">
                        ${vote.status === 'active' ? '进行中' : '已结束'}
                    </span>
                </div>

                <div class="vote-meta">
                    <div class="vote-author">
                        <i class="fas fa-user"></i>
                        <span>${creator.name}</span>
                        ${creator.title ? `<span class="user-title" style="background-color: ${creator.titleColor}">${creator.title}</span>` : ''}
                    </div>
                    <div class="vote-time">
                        <i class="fas fa-clock"></i>
                        <span>${timeAgo}</span>
                    </div>
                </div>

                ${vote.description ? `<div class="vote-description">${this.escapeHtml(vote.description)}</div>` : ''}

                <div class="vote-stats-inline">
                    <div class="vote-participants">
                        <i class="fas fa-users"></i>
                        <span>${vote.totalVotes} 人参与</span>
                    </div>
                    <div class="vote-deadline">
                        <i class="fas fa-calendar-alt"></i>
                        <span>${deadlineText}</span>
                    </div>
                </div>

                <div class="vote-actions">
                    ${canVote ? `
                        <button class="vote-btn btn-vote" data-action="vote" data-vote-id="${vote.id}">
                            <i class="fas fa-vote-yea"></i>
                            投票
                        </button>
                    ` : hasVoted ? `
                        <button class="vote-btn btn-disabled" disabled>
                            <i class="fas fa-check"></i>
                            已投票
                        </button>
                    ` : `
                        <button class="vote-btn btn-disabled" disabled>
                            <i class="fas fa-ban"></i>
                            ${this.state.isLoggedIn ? '无法投票' : '请先登录'}
                        </button>
                    `}

                    <button class="vote-btn btn-view" data-action="view" data-vote-id="${vote.id}">
                        <i class="fas fa-eye"></i>
                        查看详情
                    </button>

                    ${canDelete ? `
                        <button class="vote-btn btn-delete" data-action="delete" data-vote-id="${vote.id}">
                            <i class="fas fa-trash"></i>
                            删除
                        </button>
                    ` : ''}
                </div>
            </div>
        `;
    }

    /**
     * 获取创建者信息
     * @param {string} userId - 用户ID
     * @returns {Object} 创建者信息
     */
    getCreatorInfo(userId) {
        // 这里应该从用户系统获取用户信息
        // 暂时使用简单的映射
        const userMap = {
            'UID_123456': { name: 'admin', title: '管理员', titleColor: '#e74c3c' },
            'UID_654321': { name: 'test', title: null, titleColor: null }
        };

        return userMap[userId] || { name: '未知用户', title: null, titleColor: null };
    }

    /**
     * 渲染空状态
     */
    renderEmptyState() {
        const emptyHtml = `
            <div class="empty-state">
                <i class="fas fa-vote-yea"></i>
                <h3>暂无投票</h3>
                <p>还没有任何投票，快来创建第一个投票吧！</p>
                ${this.state.isLoggedIn ? `
                    <button class="create-first-vote" onclick="votingApp.openCreateVoteModal()">
                        <i class="fas fa-plus"></i>
                        创建投票
                    </button>
                ` : `
                    <button class="create-first-vote" onclick="votingApp.openLoginModal()">
                        <i class="fas fa-sign-in-alt"></i>
                        登录后创建投票
                    </button>
                `}
            </div>
        `;

        this.elements.votingList.innerHTML = emptyHtml;
    }

    /**
     * 更新统计信息
     */
    updateStats() {
        const stats = this.voteSystem.getVoteStats(
            this.state.currentUser ? this.state.currentUser.uid : null
        );

        if (this.elements.totalVotes) {
            this.elements.totalVotes.textContent = stats.totalVotes;
        }
        if (this.elements.activeVotes) {
            this.elements.activeVotes.textContent = stats.activeVotes;
        }
        if (this.elements.myVotes) {
            this.elements.myVotes.textContent = stats.myVotes;
        }
    }

    /**
     * HTML转义
     * @param {string} text - 需要转义的文本
     * @returns {string} 转义后的文本
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 显示错误消息
     * @param {string} message - 错误消息
     */
    showError(message) {
        console.error(message);
        // 这里可以添加更好的错误显示方式
        alert(message);
    }

    /**
     * 绑定投票卡片事件
     */
    bindVoteCardEvents() {
        const voteCards = document.querySelectorAll('.vote-card');
        voteCards.forEach(card => {
            const buttons = card.querySelectorAll('.vote-btn[data-action]');
            buttons.forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const action = btn.dataset.action;
                    const voteId = btn.dataset.voteId;

                    switch (action) {
                        case 'vote':
                            this.openVoteDetailModal(voteId, true);
                            break;
                        case 'view':
                            this.openVoteDetailModal(voteId, false);
                            break;
                        case 'delete':
                            this.openDeleteConfirmModal(voteId);
                            break;
                    }
                });
            });
        });
    }

    /**
     * 渲染分页
     * @param {Object} pagination - 分页信息
     */
    renderPagination(pagination) {
        if (!this.elements.paginationContainer) return;

        if (pagination.totalPages <= 1) {
            this.elements.paginationContainer.innerHTML = '';
            return;
        }

        let paginationHtml = `
            <button class="pagination-btn" ${!pagination.hasPrev ? 'disabled' : ''}
                    onclick="votingApp.goToPage(${pagination.currentPage - 1})">
                <i class="fas fa-chevron-left"></i>
            </button>
        `;

        // 显示页码
        const startPage = Math.max(1, pagination.currentPage - 2);
        const endPage = Math.min(pagination.totalPages, pagination.currentPage + 2);

        if (startPage > 1) {
            paginationHtml += `
                <button class="pagination-btn" onclick="votingApp.goToPage(1)">1</button>
                ${startPage > 2 ? '<span class="pagination-ellipsis">...</span>' : ''}
            `;
        }

        for (let i = startPage; i <= endPage; i++) {
            paginationHtml += `
                <button class="pagination-btn ${i === pagination.currentPage ? 'active' : ''}"
                        onclick="votingApp.goToPage(${i})">${i}</button>
            `;
        }

        if (endPage < pagination.totalPages) {
            paginationHtml += `
                ${endPage < pagination.totalPages - 1 ? '<span class="pagination-ellipsis">...</span>' : ''}
                <button class="pagination-btn" onclick="votingApp.goToPage(${pagination.totalPages})">${pagination.totalPages}</button>
            `;
        }

        paginationHtml += `
            <button class="pagination-btn" ${!pagination.hasNext ? 'disabled' : ''}
                    onclick="votingApp.goToPage(${pagination.currentPage + 1})">
                <i class="fas fa-chevron-right"></i>
            </button>
        `;

        paginationHtml += `
            <div class="pagination-info">
                第 ${pagination.currentPage} 页，共 ${pagination.totalPages} 页，${pagination.totalVotes} 个投票
            </div>
        `;

        this.elements.paginationContainer.innerHTML = paginationHtml;
    }

    /**
     * 跳转到指定页面
     * @param {number} page - 页码
     */
    goToPage(page) {
        this.state.currentPage = page;
        this.loadVotes();
    }

    /**
     * 打开创建投票弹窗
     */
    openCreateVoteModal() {
        if (!this.state.isLoggedIn) {
            this.openLoginModal();
            return;
        }

        // 检查每日创建限制
        const todayCount = this.voteSystem.getTodayVoteCount(this.state.currentUser.uid);
        if (todayCount >= 2) {
            this.showNotification('createVoteNotification', '每天最多只能创建2个投票', 'error');
            return;
        }

        // 重置表单
        this.resetCreateVoteForm();

        // 显示弹窗
        this.elements.createVoteModal.classList.add('active');
        document.body.style.overflow = 'hidden';

        // 设置默认截止时间
        this.setDefaultDeadline();
    }

    /**
     * 关闭创建投票弹窗
     */
    closeCreateVoteModal() {
        this.elements.createVoteModal.classList.remove('active');
        document.body.style.overflow = '';
        this.resetCreateVoteForm();
    }

    /**
     * 重置创建投票表单
     */
    resetCreateVoteForm() {
        // 重置基本信息
        this.elements.voteTitle.value = '';
        this.elements.voteDescription.value = '';

        // 重置选项
        this.elements.voteOptions.innerHTML = `
            <div class="option-item">
                <input type="text" class="option-input" placeholder="选项一" maxlength="50">
                <button class="remove-option" style="display: none;">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="option-item">
                <input type="text" class="option-input" placeholder="选项二" maxlength="50">
                <button class="remove-option" style="display: none;">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        // 重置截止时间
        this.elements.enableDeadline.checked = false;
        this.elements.deadlineSettings.style.display = 'none';
        this.elements.deadlineDate.value = '';
        this.elements.deadlineTime.value = '';

        // 重置设置
        this.elements.allowMultiple.checked = false;
        this.elements.showResults.checked = true;
        this.elements.anonymousVote.checked = false;

        // 重置字符计数
        this.updateCharCount('title');
        this.updateCharCount('description');

        // 重置通知
        this.hideNotification('createVoteNotification');

        // 重新绑定选项事件
        this.bindOptionEvents();
    }

    /**
     * 设置默认截止时间
     */
    setDefaultDeadline() {
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);

        const dateStr = tomorrow.toISOString().split('T')[0];
        const timeStr = '23:59';

        this.elements.deadlineDate.value = dateStr;
        this.elements.deadlineTime.value = timeStr;
    }

    /**
     * 添加投票选项
     */
    addVoteOption() {
        const optionCount = this.elements.voteOptions.children.length;
        if (optionCount >= 10) {
            this.showNotification('createVoteNotification', '最多只能添加10个选项', 'warning');
            return;
        }

        const optionHtml = `
            <div class="option-item">
                <input type="text" class="option-input" placeholder="选项${optionCount + 1}" maxlength="50">
                <button class="remove-option">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        this.elements.voteOptions.insertAdjacentHTML('beforeend', optionHtml);
        this.bindOptionEvents();
        this.updateRemoveButtonsVisibility();
    }

    /**
     * 绑定选项事件
     */
    bindOptionEvents() {
        const removeButtons = this.elements.voteOptions.querySelectorAll('.remove-option');
        removeButtons.forEach(btn => {
            btn.removeEventListener('click', this.handleRemoveOption);
            btn.addEventListener('click', this.handleRemoveOption.bind(this));
        });
    }

    /**
     * 处理删除选项
     * @param {Event} e - 事件对象
     */
    handleRemoveOption(e) {
        const optionItem = e.target.closest('.option-item');
        const optionCount = this.elements.voteOptions.children.length;

        if (optionCount <= 2) {
            this.showNotification('createVoteNotification', '至少需要保留2个选项', 'warning');
            return;
        }

        optionItem.remove();
        this.updateRemoveButtonsVisibility();
    }

    /**
     * 更新删除按钮可见性
     */
    updateRemoveButtonsVisibility() {
        const removeButtons = this.elements.voteOptions.querySelectorAll('.remove-option');
        const optionCount = removeButtons.length;

        removeButtons.forEach(btn => {
            btn.style.display = optionCount > 2 ? 'flex' : 'none';
        });
    }

    /**
     * 切换截止时间设置
     * @param {boolean} enabled - 是否启用
     */
    toggleDeadlineSettings(enabled) {
        this.elements.deadlineSettings.style.display = enabled ? 'block' : 'none';
    }

    /**
     * 更新字符计数
     * @param {string} type - 类型（title/description）
     */
    updateCharCount(type) {
        if (type === 'title') {
            const count = this.elements.voteTitle.value.length;
            this.elements.titleCharCount.textContent = count;
            this.elements.titleCharCount.style.color = count > 90 ? '#e74c3c' : '#999';
        } else if (type === 'description') {
            const count = this.elements.voteDescription.value.length;
            this.elements.descCharCount.textContent = count;
            this.elements.descCharCount.style.color = count > 450 ? '#e74c3c' : '#999';
        }
    }

    /**
     * 处理创建投票
     */
    handleCreateVote() {
        if (this.state.isCreatingVote) return;

        this.state.isCreatingVote = true;
        this.elements.createVoteBtn.disabled = true;
        this.elements.createVoteBtn.textContent = '创建中...';

        try {
            // 收集表单数据
            const voteData = this.collectVoteFormData();

            // 创建投票
            const result = this.voteSystem.createVote(voteData);

            if (result.success) {
                this.showNotification('createVoteNotification', result.message, 'success');

                // 延迟关闭弹窗并刷新列表
                setTimeout(() => {
                    this.closeCreateVoteModal();
                    this.loadVotes();
                    this.updateStats();
                }, 1500);
            } else {
                this.showNotification('createVoteNotification', result.message, 'error');
            }
        } catch (error) {
            console.error('创建投票失败:', error);
            this.showNotification('createVoteNotification', '创建投票时发生错误，请重试', 'error');
        } finally {
            this.state.isCreatingVote = false;
            this.elements.createVoteBtn.disabled = false;
            this.elements.createVoteBtn.textContent = '创建投票';
        }
    }

    /**
     * 收集投票表单数据
     * @returns {Object} 投票数据
     */
    collectVoteFormData() {
        // 基本信息
        const title = this.elements.voteTitle.value.trim();
        const description = this.elements.voteDescription.value.trim();

        // 选项
        const optionInputs = this.elements.voteOptions.querySelectorAll('.option-input');
        const options = Array.from(optionInputs)
            .map(input => input.value.trim())
            .filter(option => option.length > 0);

        // 截止时间
        let deadline = null;
        if (this.elements.enableDeadline.checked) {
            const date = this.elements.deadlineDate.value;
            const time = this.elements.deadlineTime.value;
            if (date && time) {
                deadline = new Date(`${date}T${time}`).getTime();
            }
        }

        // 设置
        const allowMultiple = this.elements.allowMultiple.checked;
        const showResults = this.elements.showResults.checked;
        const anonymous = this.elements.anonymousVote.checked;

        return {
            title,
            description,
            options,
            deadline,
            allowMultiple,
            showResults,
            anonymous
        };
    }

    /**
     * 打开投票详情弹窗
     * @param {string} voteId - 投票ID
     * @param {boolean} forVoting - 是否用于投票
     */
    openVoteDetailModal(voteId, forVoting = false) {
        const result = this.voteSystem.getVoteDetail(voteId);

        if (!result.success) {
            this.showError(result.message);
            return;
        }

        this.renderVoteDetail(result.vote, result.userVote, result.canVote, result.canDelete, forVoting);
        this.elements.voteDetailModal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    /**
     * 关闭投票详情弹窗
     */
    closeVoteDetailModal() {
        this.elements.voteDetailModal.classList.remove('active');
        document.body.style.overflow = '';
    }

    /**
     * 渲染投票详情
     * @param {Object} vote - 投票对象
     * @param {Array} userVote - 用户投票记录
     * @param {boolean} canVote - 是否可以投票
     * @param {boolean} canDelete - 是否可以删除
     * @param {boolean} forVoting - 是否用于投票
     */
    renderVoteDetail(vote, userVote, canVote, canDelete, forVoting) {
        const creator = this.getCreatorInfo(vote.createdBy);
        const timeAgo = this.voteSystem.formatTime(vote.createdAt);
        const deadlineText = this.voteSystem.formatDeadline(vote.deadline);

        // 设置标题
        document.getElementById('voteDetailTitle').textContent = forVoting ? '参与投票' : '投票详情';

        const contentHtml = `
            <div class="vote-detail-info">
                <h2>${this.escapeHtml(vote.title)}</h2>
                ${vote.description ? `<p class="vote-description">${this.escapeHtml(vote.description)}</p>` : ''}

                <div class="detail-meta">
                    <div class="meta-item">
                        <div class="meta-label">发起人</div>
                        <div class="meta-value">
                            ${creator.name}
                            ${creator.title ? `<span class="user-title" style="background-color: ${creator.titleColor}">${creator.title}</span>` : ''}
                        </div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">发起时间</div>
                        <div class="meta-value">${timeAgo}</div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">截止时间</div>
                        <div class="meta-value">${deadlineText}</div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">参与人数</div>
                        <div class="meta-value">${vote.totalVotes} 人</div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">投票状态</div>
                        <div class="meta-value">
                            <span class="vote-status ${vote.status === 'active' ? 'status-active' : 'status-ended'}">
                                ${vote.status === 'active' ? '进行中' : '已结束'}
                            </span>
                        </div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">投票设置</div>
                        <div class="meta-value">
                            ${vote.settings.allowMultiple ? '多选' : '单选'}
                            ${vote.settings.showResults ? ' · 实时显示结果' : ''}
                            ${vote.settings.anonymous ? ' · 匿名投票' : ''}
                        </div>
                    </div>
                </div>
            </div>

            <div class="vote-options-detail">
                <h3>投票选项</h3>
                ${this.renderVoteOptions(vote, userVote, canVote, forVoting)}
            </div>

            ${canVote && forVoting ? `
                <div class="modal-buttons">
                    <button class="cancel-btn" onclick="votingApp.closeVoteDetailModal()">取消</button>
                    <button class="submit-btn" id="submitVoteBtn" onclick="votingApp.handleSubmitVote('${vote.id}')">提交投票</button>
                </div>
            ` : `
                <div class="modal-buttons">
                    ${canDelete ? `
                        <button class="delete-btn" onclick="votingApp.openDeleteConfirmModal('${vote.id}')">
                            <i class="fas fa-trash"></i>
                            删除投票
                        </button>
                    ` : ''}
                    <button class="cancel-btn" onclick="votingApp.closeVoteDetailModal()">关闭</button>
                </div>
            `}
        `;

        document.getElementById('voteDetailContent').innerHTML = contentHtml;
    }

    /**
     * 渲染投票选项
     * @param {Object} vote - 投票对象
     * @param {Array} userVote - 用户投票记录
     * @param {boolean} canVote - 是否可以投票
     * @param {boolean} forVoting - 是否用于投票
     * @returns {string} HTML字符串
     */
    renderVoteOptions(vote, userVote, canVote, forVoting) {
        return vote.options.map(option => {
            const percentage = vote.totalVotes > 0 ? (option.votes / vote.totalVotes * 100).toFixed(1) : 0;
            const isSelected = userVote && userVote.includes(option.id);
            const inputType = vote.settings.allowMultiple ? 'checkbox' : 'radio';

            return `
                <div class="option-detail ${isSelected ? 'selected' : ''}" data-option-id="${option.id}">
                    <div class="option-header">
                        <div class="option-text">
                            ${canVote && forVoting ? `
                                <label style="display: flex; align-items: center; gap: 10px; cursor: pointer;">
                                    <input type="${inputType}" name="voteOption" value="${option.id}"
                                           ${isSelected ? 'checked' : ''}
                                           style="margin: 0;">
                                    <span>${this.escapeHtml(option.text)}</span>
                                </label>
                            ` : `
                                <span>${this.escapeHtml(option.text)}</span>
                                ${isSelected ? '<i class="fas fa-check" style="color: var(--primary-color); margin-left: 10px;"></i>' : ''}
                            `}
                        </div>
                        ${vote.settings.showResults || !canVote ? `
                            <div class="option-votes">${option.votes} 票 (${percentage}%)</div>
                        ` : ''}
                    </div>
                    ${vote.settings.showResults || !canVote ? `
                        <div class="option-progress">
                            <div class="progress-bar" style="width: ${percentage}%"></div>
                        </div>
                    ` : ''}
                </div>
            `;
        }).join('');
    }

    /**
     * 处理提交投票
     * @param {string} voteId - 投票ID
     */
    handleSubmitVote(voteId) {
        const selectedOptions = Array.from(document.querySelectorAll('input[name="voteOption"]:checked'))
            .map(input => input.value);

        if (selectedOptions.length === 0) {
            alert('请选择投票选项');
            return;
        }

        const result = this.voteSystem.submitVote(voteId, selectedOptions);

        if (result.success) {
            alert(result.message);
            this.closeVoteDetailModal();
            this.loadVotes();
            this.updateStats();
        } else {
            alert(result.message);
        }
    }

    /**
     * 打开删除确认弹窗
     * @param {string} voteId - 投票ID
     */
    openDeleteConfirmModal(voteId) {
        this.state.selectedVoteId = voteId;
        this.elements.deleteConfirmModal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    /**
     * 关闭删除确认弹窗
     */
    closeDeleteConfirmModal() {
        this.state.selectedVoteId = null;
        this.elements.deleteConfirmModal.classList.remove('active');
        document.body.style.overflow = '';
    }

    /**
     * 处理删除投票
     */
    handleDeleteVote() {
        if (!this.state.selectedVoteId) return;

        const result = this.voteSystem.deleteVote(this.state.selectedVoteId);

        if (result.success) {
            alert(result.message);
            this.closeDeleteConfirmModal();
            this.closeVoteDetailModal();
            this.loadVotes();
            this.updateStats();
        } else {
            alert(result.message);
        }
    }

    /**
     * 打开登录弹窗
     */
    openLoginModal() {
        this.elements.loginModal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    /**
     * 打开个人信息弹窗
     */
    openProfileModal() {
        this.elements.profileModal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    /**
     * 显示通知
     * @param {string} elementId - 通知元素ID
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型
     */
    showNotification(elementId, message, type) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = message;
            element.className = `notification ${type}`;
        }
    }

    /**
     * 隐藏通知
     * @param {string} elementId - 通知元素ID
     */
    hideNotification(elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            element.className = 'notification';
        }
    }
}

// 创建全局实例
let votingApp;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    votingApp = new VotingApp();
});
