/* 投票页面专用样式 */

/* 页面主体样式 */
.voting-main {
    margin-top: 80px; /* 为固定导航栏留出空间 */
    min-height: calc(100vh - 80px);
    background: linear-gradient(135deg, var(--accent-color) 0%, var(--secondary-color) 100%);
}

/* 页面标题区域 */
.page-header {
    padding: 60px 0 40px;
    text-align: center;
    background: linear-gradient(135deg, var(--primary-color), var(--light-purple));
    color: var(--secondary-color);
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.page-title {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 1;
}

.page-title i {
    margin-right: 15px;
    color: rgba(255, 255, 255, 0.9);
}

.page-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    position: relative;
    z-index: 1;
}

/* 投票内容区域 */
.voting-content {
    padding: 40px 0;
}

/* 投票统计信息 */
.voting-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.stat-item {
    background: var(--secondary-color);
    border-radius: 15px;
    padding: 25px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(94, 53, 177, 0.1);
}

.stat-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 8px;
}

.stat-label {
    color: #666;
    font-size: 0.9rem;
    font-weight: 500;
}

/* 投票控制区域 */
.voting-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: var(--secondary-color);
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.filter-section {
    display: flex;
    gap: 10px;
}

.filter-btn {
    padding: 8px 16px;
    border: 2px solid var(--primary-color);
    background: transparent;
    color: var(--primary-color);
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--primary-color);
    color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(94, 53, 177, 0.3);
}

.sort-section {
    display: flex;
    align-items: center;
    gap: 10px;
}

.sort-select {
    padding: 8px 12px;
    border: 2px solid var(--primary-color);
    border-radius: 8px;
    background: var(--secondary-color);
    color: var(--primary-color);
    cursor: pointer;
    transition: all 0.3s ease;
}

.sort-select:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(94, 53, 177, 0.2);
}

/* 投票列表 */
.voting-list {
    display: grid;
    gap: 25px;
    margin-bottom: 40px;
}

.vote-card {
    background: var(--secondary-color);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(94, 53, 177, 0.1);
    position: relative;
}

.vote-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.vote-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.vote-title {
    font-size: 1.3rem;
    font-weight: bold;
    color: var(--primary-color);
    margin: 0;
    flex: 1;
    margin-right: 15px;
}

.vote-status {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
    text-align: center;
}

.status-active {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
}

.status-ended {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
    color: white;
}

.vote-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    font-size: 0.9rem;
    color: #666;
}

.vote-author {
    display: flex;
    align-items: center;
    gap: 8px;
}

.vote-author i {
    color: var(--primary-color);
}

.vote-time {
    display: flex;
    align-items: center;
    gap: 8px;
}

.vote-time i {
    color: var(--primary-color);
}

.vote-description {
    color: #555;
    margin-bottom: 20px;
    line-height: 1.6;
}

.vote-stats-inline {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(94, 53, 177, 0.05);
    border-radius: 10px;
}

.vote-participants {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--primary-color);
    font-weight: 500;
}

.vote-deadline {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #e74c3c;
    font-weight: 500;
}

.vote-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.vote-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.btn-vote {
    background: linear-gradient(135deg, var(--primary-color), var(--light-purple));
    color: var(--secondary-color);
}

.btn-vote:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(94, 53, 177, 0.3);
}

.btn-view {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.btn-view:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.btn-delete {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.btn-delete:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

.btn-disabled {
    background: #bdc3c7;
    color: #7f8c8d;
    cursor: not-allowed;
}

.btn-disabled:hover {
    transform: none;
    box-shadow: none;
}

/* 新建投票悬浮按钮 */
.create-vote-fab {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--light-purple));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 8px 25px rgba(94, 53, 177, 0.3);
    transition: all 0.3s ease;
    z-index: 1000;
    color: var(--secondary-color);
    font-size: 1.5rem;
}

.create-vote-fab:hover {
    transform: scale(1.1);
    box-shadow: 0 12px 35px rgba(94, 53, 177, 0.4);
}

.fab-tooltip {
    position: absolute;
    right: 70px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.9rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.create-vote-fab:hover .fab-tooltip {
    opacity: 1;
    visibility: visible;
}

/* 弹窗样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.vote-modal,
.vote-detail-modal,
.confirm-modal {
    background: var(--secondary-color);
    border-radius: 15px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
    transform: translateY(-20px);
    transition: all 0.4s ease;
    /* 优化滚动体验 */
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color) transparent;
}

/* 自定义滚动条样式 */
.vote-modal::-webkit-scrollbar,
.vote-detail-modal::-webkit-scrollbar {
    width: 6px;
}

.vote-modal::-webkit-scrollbar-track,
.vote-detail-modal::-webkit-scrollbar-track {
    background: transparent;
}

.vote-modal::-webkit-scrollbar-thumb,
.vote-detail-modal::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 3px;
}

.vote-modal::-webkit-scrollbar-thumb:hover,
.vote-detail-modal::-webkit-scrollbar-thumb:hover {
    background: var(--light-purple);
}

.modal-overlay.active .vote-modal,
.modal-overlay.active .vote-detail-modal,
.modal-overlay.active .confirm-modal {
    transform: translateY(0);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid rgba(94, 53, 177, 0.1);
}

.modal-title {
    font-size: 1.3rem;
    font-weight: bold;
    color: var(--primary-color);
    margin: 0;
}

.close-modal {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #999;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close-modal:hover {
    color: var(--primary-color);
}

.modal-content {
    padding: 25px;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--primary-color);
}

.form-control {
    width: 100%;
    padding: 12px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(94, 53, 177, 0.1);
}

.char-count {
    text-align: right;
    font-size: 0.8rem;
    color: #999;
    margin-top: 5px;
}

/* 投票选项 */
.vote-options {
    margin-bottom: 15px;
}

.option-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.option-input {
    flex: 1;
    padding: 10px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.option-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(94, 53, 177, 0.1);
}

.remove-option {
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.remove-option:hover {
    background: #c0392b;
    transform: scale(1.1);
}

.add-option-btn {
    background: transparent;
    border: 2px dashed var(--primary-color);
    color: var(--primary-color);
    padding: 10px 15px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    justify-content: center;
}

.add-option-btn:hover {
    background: rgba(94, 53, 177, 0.1);
}

/* 截止时间设置 */
.deadline-toggle {
    margin-bottom: 15px;
}

.toggle-label {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    font-weight: 500;
    padding: 10px 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
    min-height: 44px;
    position: relative;
}

.toggle-label:hover {
    background: rgba(94, 53, 177, 0.05);
}

.toggle-label input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.toggle-slider {
    width: 52px;
    height: 26px;
    background: #ccc;
    border-radius: 26px;
    position: relative;
    transition: all 0.3s ease;
    flex-shrink: 0;
    display: inline-block;
}

.toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 22px;
    height: 22px;
    background: white;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggle-label input[type="checkbox"]:checked + .toggle-slider {
    background: var(--primary-color);
}

.toggle-label input[type="checkbox"]:checked + .toggle-slider::before {
    transform: translateX(26px);
}

.toggle-label span:last-child {
    flex: 1;
    font-size: 0.95rem;
    line-height: 1.4;
    user-select: none;
}

.deadline-settings {
    padding: 20px;
    background: rgba(94, 53, 177, 0.05);
    border-radius: 10px;
    border: 1px solid rgba(94, 53, 177, 0.1);
}

.datetime-group {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.date-input,
.time-input {
    display: flex;
    flex-direction: column;
}

.date-input label,
.time-input label {
    margin-bottom: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--primary-color);
}

.date-input input,
.time-input input {
    padding: 10px 12px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.date-input input:focus,
.time-input input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(94, 53, 177, 0.1);
}

/* 投票设置 */
.vote-settings {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.setting-item {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    padding: 10px 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
    min-height: 44px; /* 确保最小高度 */
    position: relative;
}

.setting-item:hover {
    background: rgba(94, 53, 177, 0.05);
}

.setting-item input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.checkmark {
    width: 22px;
    height: 22px;
    border: 2px solid var(--primary-color);
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
    flex-shrink: 0; /* 防止收缩 */
    background: var(--secondary-color);
    display: flex;
    align-items: center;
    justify-content: center;
}

.setting-item input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.setting-item input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    color: white;
    font-size: 14px;
    font-weight: bold;
    line-height: 1;
}

.setting-item span:last-child {
    flex: 1;
    font-size: 0.95rem;
    line-height: 1.4;
    word-wrap: break-word;
    user-select: none;
}

/* 按钮样式 */
.modal-buttons {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 30px;
}

.cancel-btn,
.submit-btn,
.delete-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.cancel-btn {
    background: #95a5a6;
    color: white;
}

.cancel-btn:hover {
    background: #7f8c8d;
    transform: translateY(-2px);
}

.submit-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--light-purple));
    color: var(--secondary-color);
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(94, 53, 177, 0.3);
}

.delete-btn {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.delete-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

/* 确认弹窗样式 */
.confirm-modal {
    max-width: 400px;
}

.confirm-message {
    text-align: center;
    padding: 20px 0;
}

.confirm-message i {
    font-size: 3rem;
    color: #f39c12;
    margin-bottom: 15px;
}

.confirm-message p {
    margin: 10px 0;
    color: #555;
}

.warning-text {
    color: #e74c3c;
    font-size: 0.9rem;
    font-style: italic;
}

/* 投票详情样式 */
.vote-detail-info {
    margin-bottom: 25px;
}

.detail-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(94, 53, 177, 0.05);
    border-radius: 10px;
}

.meta-item {
    text-align: center;
}

.meta-label {
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 5px;
}

.meta-value {
    font-weight: bold;
    color: var(--primary-color);
}

.vote-options-detail {
    margin-bottom: 25px;
}

.option-detail {
    margin-bottom: 15px;
    padding: 15px;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.option-detail.selected {
    border-color: var(--primary-color);
    background: rgba(94, 53, 177, 0.05);
}

.option-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.option-text {
    font-weight: 500;
    color: var(--primary-color);
}

.option-votes {
    font-size: 0.9rem;
    color: #666;
}

.option-progress {
    width: 100%;
    height: 8px;
    background: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--light-purple));
    transition: width 0.3s ease;
}

/* 分页样式 */
.pagination-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 30px;
}

.pagination-btn {
    padding: 8px 12px;
    border: 2px solid var(--primary-color);
    background: transparent;
    color: var(--primary-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.pagination-btn:hover,
.pagination-btn.active {
    background: var(--primary-color);
    color: var(--secondary-color);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-info {
    color: #666;
    font-size: 0.9rem;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.empty-state i {
    font-size: 4rem;
    color: #bdc3c7;
    margin-bottom: 20px;
}

.empty-state h3 {
    margin-bottom: 10px;
    color: var(--primary-color);
}

.empty-state p {
    margin-bottom: 20px;
}

.empty-state .create-first-vote {
    background: linear-gradient(135deg, var(--primary-color), var(--light-purple));
    color: var(--secondary-color);
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.empty-state .create-first-vote:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(94, 53, 177, 0.3);
}

/* 通知样式 */
.notification {
    padding: 12px;
    border-radius: 8px;
    margin-bottom: 15px;
    font-weight: 500;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.notification.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    opacity: 1;
    visibility: visible;
}

.notification.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    opacity: 1;
    visibility: visible;
}

.notification.warning {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
    opacity: 1;
    visibility: visible;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .voting-stats {
        grid-template-columns: 1fr;
    }

    .voting-controls {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .filter-section {
        justify-content: center;
        flex-wrap: wrap;
    }

    .sort-section {
        justify-content: center;
    }

    .vote-header {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }

    .vote-stats-inline {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .vote-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    /* 弹窗响应式优化 */
    .datetime-group {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .modal-buttons {
        flex-direction: column;
        gap: 10px;
    }

    .modal-buttons .cancel-btn,
    .modal-buttons .submit-btn,
    .modal-buttons .delete-btn {
        width: 100%;
        text-align: center;
    }

    /* 投票设置响应式 */
    .vote-settings {
        gap: 12px;
    }

    .setting-item {
        padding: 12px 8px;
        min-height: 48px; /* 增加触摸目标大小 */
    }

    .checkmark {
        width: 24px;
        height: 24px;
    }

    .setting-item span:last-child {
        font-size: 1rem;
        line-height: 1.5;
    }

    /* 截止时间设置响应式 */
    .toggle-label {
        padding: 12px 8px;
        min-height: 48px;
    }

    .toggle-slider {
        width: 54px;
        height: 28px;
    }

    .toggle-slider::before {
        width: 24px;
        height: 24px;
    }

    .toggle-label input[type="checkbox"]:checked + .toggle-slider::before {
        transform: translateX(26px);
    }

    .toggle-label span:last-child {
        font-size: 1rem;
        line-height: 1.5;
    }

    .deadline-settings {
        padding: 15px;
    }

    .date-input input,
    .time-input input {
        padding: 12px;
        font-size: 1rem;
        min-height: 44px;
        box-sizing: border-box;
    }

    /* 选项输入响应式 */
    .option-item {
        gap: 8px;
        margin-bottom: 12px;
    }

    .option-input {
        padding: 12px;
        font-size: 1rem;
        min-height: 44px;
        box-sizing: border-box;
    }

    .remove-option {
        width: 36px;
        height: 36px;
        font-size: 1rem;
        flex-shrink: 0;
    }

    .add-option-btn {
        padding: 12px 15px;
        font-size: 1rem;
        min-height: 44px;
    }

    /* 表单控件响应式 */
    .form-control {
        padding: 12px;
        font-size: 1rem;
        min-height: 44px;
    }

    /* 悬浮按钮响应式 */
    .create-vote-fab {
        bottom: 20px;
        right: 20px;
        width: 56px;
        height: 56px;
        font-size: 1.3rem;
    }

    .fab-tooltip {
        display: none;
    }

    /* 弹窗大小响应式 */
    .vote-modal,
    .vote-detail-modal,
    .confirm-modal {
        width: 95%;
        max-width: none;
        margin: 10px;
        max-height: 95vh;
    }

    .modal-content {
        padding: 20px;
    }

    .modal-header {
        padding: 15px 20px;
    }

    /* 投票详情响应式 */
    .detail-meta {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .meta-item {
        text-align: left;
        padding: 10px;
        background: rgba(255, 255, 255, 0.5);
        border-radius: 6px;
    }

    .option-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .option-text label {
        font-size: 1rem;
        line-height: 1.5;
    }

    .option-text input[type="radio"],
    .option-text input[type="checkbox"] {
        width: 18px;
        height: 18px;
        margin-right: 8px;
    }
}

/* 更小屏幕的额外优化 */
@media (max-width: 480px) {
    .page-title {
        font-size: 2rem;
    }

    .page-subtitle {
        font-size: 1rem;
    }

    .voting-stats {
        gap: 15px;
    }

    .stat-item {
        padding: 20px 15px;
    }

    .stat-number {
        font-size: 2rem;
    }

    .vote-modal,
    .vote-detail-modal {
        width: 98%;
        margin: 5px;
        max-height: 98vh;
    }

    .modal-content {
        padding: 15px;
    }

    .modal-header {
        padding: 12px 15px;
    }

    .modal-title {
        font-size: 1.1rem;
    }

    .form-group {
        margin-bottom: 15px;
    }

    .vote-settings {
        gap: 10px;
    }

    .datetime-group {
        gap: 12px;
    }

    .deadline-settings {
        padding: 12px;
    }
}

/* 横屏模式优化 */
@media (max-width: 768px) and (orientation: landscape) {
    .vote-modal,
    .vote-detail-modal {
        max-height: 90vh;
        overflow-y: auto;
    }

    .modal-content {
        padding: 15px;
    }

    .datetime-group {
        grid-template-columns: 1fr 1fr;
        gap: 10px;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    /* 增加所有可点击元素的触摸目标大小 */
    .filter-btn,
    .vote-btn,
    .pagination-btn {
        min-height: 44px;
        padding: 12px 16px;
    }

    .close-modal {
        min-width: 44px;
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* 优化表单元素的触摸体验 */
    .form-control,
    .option-input,
    .sort-select {
        min-height: 44px;
        font-size: 16px; /* 防止iOS缩放 */
    }

    /* 优化切换开关的触摸区域 */
    .toggle-label,
    .setting-item {
        min-height: 48px;
        padding: 12px;
    }

    /* 优化按钮的触摸反馈 */
    .vote-btn:active,
    .filter-btn:active,
    .pagination-btn:active,
    .submit-btn:active,
    .cancel-btn:active,
    .delete-btn:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }

    /* 优化悬浮按钮的触摸体验 */
    .create-vote-fab {
        width: 60px;
        height: 60px;
    }

    .create-vote-fab:active {
        transform: scale(0.95);
    }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .checkmark,
    .toggle-slider,
    .remove-option {
        border-width: 1px;
    }

    .vote-modal,
    .vote-detail-modal,
    .confirm-modal {
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }
}

/* 确保复选框和切换开关在所有浏览器中正确显示 */
.setting-item,
.toggle-label {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.checkmark,
.toggle-slider {
    -webkit-tap-highlight-color: transparent;
}

/* 修复可能的布局问题 */
.vote-settings,
.deadline-toggle {
    width: 100%;
    box-sizing: border-box;
}

.setting-item {
    width: 100%;
    box-sizing: border-box;
    justify-content: flex-start;
}

.toggle-label {
    width: 100%;
    box-sizing: border-box;
    justify-content: flex-start;
}

/* 确保文本不会被截断 */
.setting-item span:last-child,
.toggle-label span:last-child {
    white-space: normal;
    overflow-wrap: break-word;
    hyphens: auto;
}

/* 导航栏活跃状态 */
.nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
}

/* 登录提示样式 */
.login-prompt {
    text-align: center;
    padding: 40px 20px;
    background: var(--secondary-color);
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    margin: 20px 0;
}

.login-prompt i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.login-prompt h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
}

.login-prompt p {
    color: #666;
    margin-bottom: 20px;
}

.login-prompt .login-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--light-purple));
    color: var(--secondary-color);
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.login-prompt .login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(94, 53, 177, 0.3);
}
