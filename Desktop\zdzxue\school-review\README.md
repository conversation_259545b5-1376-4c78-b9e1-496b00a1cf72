# 校评页面

重庆市梁平区知德中学校园评论系统

## 功能特性

### 🎨 界面设计
- 紫色与白色主色调，简洁美观
- 响应式设计，支持移动端
- 视差滚动效果
- 平滑动画过渡

### 💬 评论功能
- 发表评论（标题+内容）
- 图片上传（支持多张图片）
- 自定义评论框颜色
- 点赞/踩功能
- 回复评论
- 删除自己的评论

### 🔍 搜索与排序
- 关键词搜索
- 多种排序方式：
  - 时间（从新到旧/从旧到新）
  - 点赞数（从多到少/从少到多）

### 📱 用户体验
- 草稿自动保存
- 分页加载
- 字符计数
- 实时预览
- 图片查看器

### 🔐 用户系统
- 登录验证
- 用户权限控制
- 个人信息管理

## 技术架构

### 前端技术
- **HTML5** - 页面结构
- **CSS3** - 样式设计，使用CSS变量和Flexbox/Grid布局
- **JavaScript ES6+** - 交互逻辑，使用类和模块化设计
- **Font Awesome** - 图标库
- **Pickr** - 颜色选择器

### 数据存储
- **本地存储** - 评论数据、用户信息、草稿
- **123云盘图床** - 图片存储
- **Vercel KV** - 生产环境数据存储（可选）

### 部署平台
- **Vercel** - 静态网站托管
- **GitHub** - 代码版本控制

## 文件结构

```
school-review/
├── index.html              # 主页面
├── css/
│   └── review.css          # 样式文件
├── js/
│   ├── review.js           # 主要逻辑
│   ├── comment-system.js   # 评论系统
│   └── image-upload.js     # 图片上传
└── README.md              # 说明文档
```

## 核心类说明

### SchoolReviewApp
主应用类，负责：
- 页面初始化
- 用户界面管理
- 事件绑定
- 弹窗控制

### CommentSystem
评论系统类，负责：
- 评论数据管理
- 增删改查操作
- 搜索和排序
- 草稿功能

### ImageUploader
图片上传类，负责：
- 123云盘API调用
- 文件验证
- 上传进度
- 错误处理

## API集成

### 123云盘图床API
使用123云盘开放平台的图床服务：

1. **创建文件** - 初始化上传
2. **获取上传地址** - 获取预签名URL
3. **上传文件** - 实际文件上传
4. **完成上传** - 确认上传完成
5. **轮询结果** - 异步处理结果

### 环境变量配置
```bash
# .env 文件
CLOUD123_ACCESS_TOKEN=your_access_token
CLOUD123_PARENT_FILE_ID=your_folder_id
```

## 使用说明

### 发表评论
1. 点击"分享你的校园生活"按钮
2. 填写标题和内容
3. 可选：上传图片、选择颜色
4. 点击"发表评论"

### 回复评论
1. 点击评论下方的"回复"按钮
2. 填写回复内容
3. 点击"发表回复"

### 搜索评论
1. 在搜索框输入关键词
2. 点击搜索按钮或按回车键

### 排序评论
1. 使用右上角的排序下拉菜单
2. 选择排序方式

## 响应式设计

### 桌面端 (>768px)
- 完整功能布局
- 多列图片网格
- 侧边操作按钮

### 平板端 (768px-480px)
- 调整布局间距
- 简化操作界面
- 优化触摸体验

### 移动端 (<480px)
- 单列布局
- 大号按钮
- 简化界面元素

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 性能优化

### 图片优化
- 文件大小限制（10MB）
- 支持格式：JPG、PNG、GIF、WebP、BMP
- 图片压缩和预览

### 代码优化
- 防抖函数减少API调用
- 懒加载评论列表
- 本地缓存减少网络请求

### 用户体验
- 加载动画
- 错误提示
- 操作反馈

## 安全考虑

### 输入验证
- 字符长度限制
- 文件类型检查
- XSS防护

### 权限控制
- 登录状态验证
- 操作权限检查
- 数据访问控制

## 部署说明

### 本地开发
1. 克隆项目到本地
2. 配置环境变量
3. 使用本地服务器运行

### Vercel部署
1. 连接GitHub仓库
2. 配置环境变量
3. 自动部署

## 维护说明

### 数据备份
- 定期导出评论数据
- 备份用户信息
- 图片资源备份

### 监控指标
- 页面加载速度
- API响应时间
- 错误率统计

## 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 基础评论功能
- 图片上传功能
- 搜索排序功能

## 联系方式

如有问题或建议，请联系开发团队。
