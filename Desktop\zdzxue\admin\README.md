# 后台管理系统

## 功能概述

后台管理系统为学校网站提供了完整的用户管理功能，包括用户搜索、头衔管理和禁言功能。

## 主要功能

### 1. 权限控制
- **管理员专用**：只有管理员角色的用户才能访问后台管理系统
- **导航栏按钮**：管理员登录后，导航栏中央会显示"后台管理"按钮
- **权限验证**：访问后台管理页面时会自动验证用户权限

### 2. 用户管理
- **用户列表**：显示所有注册用户的详细信息
- **搜索功能**：支持按用户名搜索用户
- **分页显示**：支持分页浏览，每页显示10个用户
- **用户信息**：显示用户头像、用户名、UID、角色、头衔和状态

### 3. 头衔系统
- **自定义头衔**：管理员可以为用户设置自定义头衔
- **颜色选择**：支持自定义头衔颜色，提供预设颜色和颜色选择器
- **实时预览**：设置头衔时可以实时预览效果
- **头衔显示**：用户在发表评论时会显示头衔
- **用户选择**：用户可以选择是否在发表评论时显示头衔

### 4. 禁言功能
- **禁言管理**：管理员可以禁言违规用户
- **禁言原因**：设置禁言时需要填写禁言原因
- **功能限制**：被禁言用户无法发表评论和回复，但可以点赞/踩
- **界面提示**：被禁言用户的评论按钮会显示为灰色，点击时提示联系管理员
- **解除禁言**：管理员可以随时解除用户的禁言状态

### 5. 统计信息
- **用户统计**：显示总用户数、有头衔用户数、被禁言用户数和管理员数量
- **实时更新**：统计信息会根据操作实时更新

## 使用说明

### 访问后台管理
1. 使用管理员账户登录（默认：admin/admin123）
2. 登录后导航栏中央会出现"后台管理"按钮
3. 点击按钮进入后台管理系统

### 搜索用户
1. 在搜索框中输入用户名
2. 点击搜索按钮或按回车键
3. 系统会显示匹配的用户列表

### 设置用户头衔
1. 在用户列表中找到目标用户
2. 点击"头衔"按钮
3. 在弹窗中输入头衔名称（最多10个字符）
4. 选择头衔颜色（可使用预设颜色或自定义）
5. 预览效果后点击"保存头衔"

### 禁言用户
1. 在用户列表中找到目标用户
2. 点击"禁言"按钮
3. 在弹窗中输入禁言原因（最多200个字符）
4. 点击"确认禁言"

### 解除禁言
1. 在用户列表中找到被禁言的用户
2. 点击"解禁"按钮
3. 确认解除禁言

## 技术实现

### 前端技术
- **HTML5**：页面结构
- **CSS3**：样式设计，包括响应式布局
- **JavaScript ES6+**：功能实现
- **Font Awesome**：图标库

### 数据存储
- **本地存储**：用户数据存储在浏览器的localStorage中
- **实时同步**：所有操作会立即同步到其他页面

### 安全特性
- **权限验证**：多层权限检查
- **输入验证**：所有用户输入都会进行验证
- **错误处理**：完善的错误处理机制

## 文件结构

```
admin/
├── index.html          # 后台管理主页面
├── css/
│   └── admin.css      # 后台管理专用样式
├── js/
│   └── admin.js       # 后台管理核心功能
└── README.md          # 说明文档
```

## 样式特色

### 设计风格
- **紫色主题**：与网站整体风格保持一致
- **现代化界面**：简洁美观的卡片式设计
- **渐变效果**：丰富的渐变色彩搭配
- **动画交互**：流畅的过渡动画

### 响应式设计
- **移动端适配**：完全支持移动设备访问
- **弹性布局**：自适应不同屏幕尺寸
- **触摸友好**：优化的触摸交互体验

## 扩展功能

### 未来可扩展的功能
1. **批量操作**：支持批量设置头衔或禁言
2. **操作日志**：记录管理员的所有操作
3. **权限分级**：支持多级管理员权限
4. **数据导出**：支持用户数据导出
5. **邮件通知**：禁言时自动发送邮件通知

## 注意事项

1. **权限管理**：请妥善保管管理员账户信息
2. **操作记录**：建议记录重要的管理操作
3. **用户体验**：禁言等操作请谨慎使用，避免影响用户体验
4. **数据备份**：定期备份用户数据
5. **浏览器兼容**：建议使用现代浏览器访问

## 联系支持

如有问题或建议，请联系开发团队。
