<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>评论框颜色应用测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@simonwep/pickr/dist/themes/classic.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/review.css">
    <style>
        body {
            padding: 20px;
            background: var(--light-gray);
        }
        .test-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            background: #f9f9f9;
        }
        .test-button {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            background: var(--hover-color);
            transform: translateY(-2px);
        }
        .status {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            font-weight: 500;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        
        .color-demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .color-demo-item {
            text-align: center;
            padding: 10px;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .color-swatch {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin: 0 auto 10px;
            border: 2px solid #ddd;
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        
        .color-swatch:hover {
            transform: scale(1.1);
        }
        
        .color-name {
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .color-code {
            font-size: 0.8rem;
            color: #666;
            font-family: monospace;
        }
        
        .current-selection {
            background: var(--accent-color);
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }
        
        .selected-color-display {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin: 0 auto 10px;
            border: 3px solid white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎨 评论框颜色应用测试</h1>
        
        <div class="test-section">
            <h3>1. 预设颜色测试</h3>
            <p>点击下方颜色块来测试不同颜色的评论框效果</p>
            
            <div class="color-demo-grid">
                <div class="color-demo-item">
                    <div class="color-swatch" style="background: #ffffff;" onclick="testColor('#ffffff')"></div>
                    <div class="color-name">纯白色</div>
                    <div class="color-code">#ffffff</div>
                </div>
                <div class="color-demo-item">
                    <div class="color-swatch" style="background: #f8f9fa;" onclick="testColor('#f8f9fa')"></div>
                    <div class="color-name">浅灰色</div>
                    <div class="color-code">#f8f9fa</div>
                </div>
                <div class="color-demo-item">
                    <div class="color-swatch" style="background: #ffe8e8;" onclick="testColor('#ffe8e8')"></div>
                    <div class="color-name">浅粉色</div>
                    <div class="color-code">#ffe8e8</div>
                </div>
                <div class="color-demo-item">
                    <div class="color-swatch" style="background: #e8f8ff;" onclick="testColor('#e8f8ff')"></div>
                    <div class="color-name">浅蓝色</div>
                    <div class="color-code">#e8f8ff</div>
                </div>
                <div class="color-demo-item">
                    <div class="color-swatch" style="background: #f0ffe8;" onclick="testColor('#f0ffe8')"></div>
                    <div class="color-name">浅绿色</div>
                    <div class="color-code">#f0ffe8</div>
                </div>
                <div class="color-demo-item">
                    <div class="color-swatch" style="background: #fff8e8;" onclick="testColor('#fff8e8')"></div>
                    <div class="color-name">浅黄色</div>
                    <div class="color-code">#fff8e8</div>
                </div>
                <div class="color-demo-item">
                    <div class="color-swatch" style="background: #5e35b1;" onclick="testColor('#5e35b1')"></div>
                    <div class="color-name">主题紫</div>
                    <div class="color-code">#5e35b1</div>
                </div>
                <div class="color-demo-item">
                    <div class="color-swatch" style="background: #2c3e50;" onclick="testColor('#2c3e50')"></div>
                    <div class="color-name">深蓝灰</div>
                    <div class="color-code">#2c3e50</div>
                </div>
            </div>
            
            <div class="current-selection">
                <div class="selected-color-display" id="selectedColorDisplay" style="background: #ffffff;"></div>
                <div><strong>当前选择:</strong> <span id="selectedColorCode">#ffffff</span></div>
            </div>
            
            <div id="colorTestStatus" class="status info">点击上方颜色块来测试评论框颜色效果</div>
        </div>
        
        <div class="test-section">
            <h3>2. 评论框颜色预览</h3>
            <p>这里显示应用了选择颜色的评论框效果</p>
            
            <div id="commentPreviewContainer">
                <!-- 评论预览将在这里动态生成 -->
            </div>
        </div>
        
        <div class="test-section">
            <h3>3. 完整功能测试</h3>
            <p>测试完整的评论发布流程</p>
            <button class="test-button" onclick="openFullCommentModal()">打开评论编辑器</button>
            <button class="test-button" onclick="addTestComment()">添加测试评论</button>
            <button class="test-button" onclick="clearTestComments()">清除测试评论</button>
            <div id="fullTestStatus" class="status info">点击上方按钮测试完整功能</div>
        </div>
    </div>

    <!-- 评论弹窗 -->
    <div class="modal-overlay" id="testCommentModalOverlay">
        <div class="comment-modal">
            <div class="modal-header">
                <h3 class="modal-title">发表评论 - 颜色测试</h3>
                <button class="modal-close" onclick="closeFullCommentModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form class="comment-form">
                    <div class="form-group">
                        <label class="form-label">标题</label>
                        <input type="text" id="testCommentTitle" class="form-input" placeholder="请输入评论标题..." value="这是一个颜色测试评论">
                    </div>
                    <div class="form-group">
                        <label class="form-label">内容</label>
                        <textarea id="testCommentContent" class="form-textarea" placeholder="分享你的校园生活...">这是一个用来测试评论框颜色功能的测试评论。您可以看到这个评论框应用了您选择的背景颜色，文字颜色会根据背景自动调整以确保可读性。</textarea>
                    </div>
                    <div class="form-actions">
                        <div class="action-buttons">
                            <button type="button" class="action-btn" id="testColorPickerBtn" onclick="openTestColorPicker()">
                                <i class="fas fa-palette"></i>
                                <span>选择颜色</span>
                            </button>
                        </div>
                    </div>
                    <div class="form-submit">
                        <button type="button" class="cancel-btn" onclick="closeFullCommentModal()">取消</button>
                        <button type="button" class="submit-btn" onclick="submitTestComment()">发表评论</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 颜色选择器 -->
    <div class="color-picker-container" id="testColorPickerContainer" style="display: none;">
        <div class="color-picker-title">
            <span>选择评论框颜色</span>
            <button type="button" class="color-picker-close" onclick="closeTestColorPicker()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="color-picker" id="testColorPicker"></div>
        <div class="color-preview-section">
            <div class="color-preview-title">预览效果</div>
            <div class="color-preview-box" id="testColorPreviewBox">
                <div class="color-preview-text">这是您的评论框颜色预览</div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/@simonwep/pickr/dist/pickr.min.js"></script>
    <script>
        let currentTestColor = '#ffffff';
        let testColorPicker = null;
        let testComments = [];
        
        // 颜色处理工具函数
        function getContrastColor(hexColor) {
            const hex = hexColor.replace('#', '');
            const r = parseInt(hex.substr(0, 2), 16);
            const g = parseInt(hex.substr(2, 2), 16);
            const b = parseInt(hex.substr(4, 2), 16);
            const brightness = (r * 299 + g * 587 + b * 114) / 1000;
            return brightness > 128 ? '#000000' : '#ffffff';
        }
        
        function isLightColor(hexColor) {
            const hex = hexColor.replace('#', '');
            const r = parseInt(hex.substr(0, 2), 16);
            const g = parseInt(hex.substr(2, 2), 16);
            const b = parseInt(hex.substr(4, 2), 16);
            const brightness = (r * 299 + g * 587 + b * 114) / 1000;
            return brightness > 128;
        }
        
        function getDarkerColor(hexColor) {
            const hex = hexColor.replace('#', '');
            let r = parseInt(hex.substr(0, 2), 16);
            let g = parseInt(hex.substr(2, 2), 16);
            let b = parseInt(hex.substr(4, 2), 16);
            
            r = Math.floor(r * 0.8);
            g = Math.floor(g * 0.8);
            b = Math.floor(b * 0.8);
            
            const toHex = (n) => {
                const hex = n.toString(16);
                return hex.length === 1 ? '0' + hex : hex;
            };
            
            return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
        }
        
        // 测试颜色
        function testColor(color) {
            currentTestColor = color;
            updateSelectedColorDisplay(color);
            renderCommentPreview(color);
            updateTestColorButton(color);
            
            const status = document.getElementById('colorTestStatus');
            status.className = 'status success';
            status.innerHTML = `✅ 已应用颜色 ${color}<br>📝 请查看下方的评论框预览效果`;
        }
        
        // 更新选择的颜色显示
        function updateSelectedColorDisplay(color) {
            const display = document.getElementById('selectedColorDisplay');
            const code = document.getElementById('selectedColorCode');
            
            if (display) display.style.background = color;
            if (code) code.textContent = color;
        }
        
        // 渲染评论预览
        function renderCommentPreview(backgroundColor) {
            const container = document.getElementById('commentPreviewContainer');
            const textColor = getContrastColor(backgroundColor);
            const isLight = isLightColor(backgroundColor);
            const borderColor = getDarkerColor(backgroundColor);
            
            const comment = {
                id: 'preview',
                title: '这是一个颜色预览评论',
                content: '这个评论展示了您选择的背景颜色效果。文字颜色会根据背景自动调整，确保良好的可读性。您可以看到标题、内容、时间、按钮等元素都会相应调整颜色。',
                author: {
                    username: '测试用户',
                    avatar: '../images/avatar.png'
                },
                createTime: new Date().toISOString(),
                likes: 5,
                dislikes: 1,
                replies: [],
                userReaction: null,
                backgroundColor: backgroundColor
            };
            
            container.innerHTML = `
                <div class="comment-card" style="background-color: ${backgroundColor}; border-left-color: ${borderColor};">
                    <div class="comment-header">
                        <div class="comment-user-info">
                            <img src="${comment.author.avatar}" alt="${comment.author.username}" class="comment-avatar">
                            <div class="comment-user-details">
                                <h4 style="color: ${textColor};">${comment.author.username}</h4>
                                <div class="comment-time" style="color: ${isLight ? '#666' : '#ccc'};">刚刚</div>
                            </div>
                        </div>
                    </div>
                    <div class="comment-content">
                        <h3 class="comment-title" style="color: ${textColor};">${comment.title}</h3>
                        <p class="comment-text" style="color: ${textColor};">${comment.content}</p>
                    </div>
                    <div class="comment-footer" style="border-top-color: ${isLight ? 'var(--accent-color)' : 'rgba(255,255,255,0.2)'};">
                        <div class="comment-stats">
                            <button class="stat-btn like-btn" style="color: ${isLight ? '#666' : '#ccc'};">
                                <i class="fas fa-thumbs-up"></i>
                                <span>${comment.likes}</span>
                            </button>
                            <button class="stat-btn dislike-btn" style="color: ${isLight ? '#666' : '#ccc'};">
                                <i class="fas fa-thumbs-down"></i>
                                <span>${comment.dislikes}</span>
                            </button>
                        </div>
                        <button class="comment-reply-btn" style="background: ${isLight ? 'var(--primary-color)' : 'rgba(255,255,255,0.9)'}; color: ${isLight ? 'var(--secondary-color)' : 'var(--primary-color)'};">
                            <i class="fas fa-reply"></i>
                            回复
                        </button>
                    </div>
                </div>
            `;
        }
        
        // 初始化颜色选择器
        function initTestColorPicker() {
            if (typeof Pickr !== 'undefined') {
                testColorPicker = Pickr.create({
                    el: '#testColorPicker',
                    theme: 'classic',
                    default: currentTestColor,
                    swatches: [
                        '#ffffff', '#f8f9fa', '#e9ecef', '#dee2e6',
                        '#ffe8e8', '#fff0e8', '#fff8e8', '#f0ffe8',
                        '#e8f8ff', '#e8f0ff', '#f0e8ff', '#ffe8f8',
                        '#5e35b1', '#7e57c2', '#9c27b0', '#e91e63',
                        '#f44336', '#ff9800', '#4caf50', '#2196f3'
                    ],
                    components: {
                        preview: true,
                        opacity: false,
                        hue: true,
                        interaction: {
                            hex: true,
                            rgba: false,
                            input: true,
                            save: true
                        }
                    }
                });
                
                testColorPicker.on('change', (color) => {
                    if (color) {
                        const colorString = color.toHEXA().toString();
                        updateTestColorPreview(colorString);
                    }
                });
                
                testColorPicker.on('save', (color) => {
                    if (color) {
                        currentTestColor = color.toHEXA().toString();
                        testColor(currentTestColor);
                    }
                    closeTestColorPicker();
                });
                
                testColorPicker.on('cancel', () => {
                    closeTestColorPicker();
                });
            }
        }
        
        // 更新测试颜色预览
        function updateTestColorPreview(color) {
            const previewBox = document.getElementById('testColorPreviewBox');
            if (previewBox) {
                previewBox.style.background = color;
                const textColor = getContrastColor(color);
                const textElement = previewBox.querySelector('.color-preview-text');
                if (textElement) {
                    textElement.style.color = textColor;
                    textElement.style.textShadow = textColor === '#000000' ? 
                        '1px 1px 2px rgba(255, 255, 255, 0.8)' : 
                        '1px 1px 2px rgba(0, 0, 0, 0.8)';
                }
            }
        }
        
        // 更新测试颜色按钮
        function updateTestColorButton(color) {
            const btn = document.getElementById('testColorPickerBtn');
            if (btn) {
                btn.style.background = `linear-gradient(45deg, ${color}, ${color}dd)`;
                btn.style.borderColor = color;
            }
        }
        
        // 打开测试颜色选择器
        function openTestColorPicker() {
            const container = document.getElementById('testColorPickerContainer');
            if (container) {
                container.style.display = 'block';
                document.body.style.overflow = 'hidden';
                if (testColorPicker) {
                    testColorPicker.show();
                }
            }
        }
        
        // 关闭测试颜色选择器
        function closeTestColorPicker() {
            const container = document.getElementById('testColorPickerContainer');
            if (container) {
                container.style.display = 'none';
                document.body.style.overflow = '';
                if (testColorPicker) {
                    testColorPicker.hide();
                }
            }
        }
        
        // 打开完整评论弹窗
        function openFullCommentModal() {
            const modal = document.getElementById('testCommentModalOverlay');
            if (modal) {
                modal.classList.add('active');
                document.body.style.overflow = 'hidden';
            }
        }
        
        // 关闭完整评论弹窗
        function closeFullCommentModal() {
            const modal = document.getElementById('testCommentModalOverlay');
            if (modal) {
                modal.classList.remove('active');
                document.body.style.overflow = '';
            }
            closeTestColorPicker();
        }
        
        // 提交测试评论
        function submitTestComment() {
            const title = document.getElementById('testCommentTitle').value;
            const content = document.getElementById('testCommentContent').value;
            
            if (!title.trim() || !content.trim()) {
                alert('请填写标题和内容');
                return;
            }
            
            addTestComment(title, content, currentTestColor);
            closeFullCommentModal();
        }
        
        // 添加测试评论
        function addTestComment(title = null, content = null, color = null) {
            const newComment = {
                id: 'test_' + Date.now(),
                title: title || `测试评论 ${testComments.length + 1}`,
                content: content || `这是第 ${testComments.length + 1} 个测试评论，使用了颜色 ${color || currentTestColor}。`,
                author: {
                    username: '测试用户',
                    avatar: '../images/avatar.png'
                },
                createTime: new Date().toISOString(),
                likes: Math.floor(Math.random() * 20),
                dislikes: Math.floor(Math.random() * 5),
                replies: [],
                userReaction: null,
                backgroundColor: color || currentTestColor
            };
            
            testComments.unshift(newComment);
            renderTestComments();
            
            const status = document.getElementById('fullTestStatus');
            status.className = 'status success';
            status.innerHTML = `✅ 已添加测试评论，使用颜色 ${newComment.backgroundColor}<br>📝 总共 ${testComments.length} 条测试评论`;
        }
        
        // 渲染测试评论
        function renderTestComments() {
            const container = document.getElementById('commentPreviewContainer');
            
            if (testComments.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #666; padding: 20px;">暂无测试评论，点击"添加测试评论"按钮创建</p>';
                return;
            }
            
            const commentsHtml = testComments.map(comment => {
                const textColor = getContrastColor(comment.backgroundColor);
                const isLight = isLightColor(comment.backgroundColor);
                const borderColor = getDarkerColor(comment.backgroundColor);
                
                return `
                    <div class="comment-card" style="background-color: ${comment.backgroundColor}; border-left-color: ${borderColor}; margin-bottom: 15px;">
                        <div class="comment-header">
                            <div class="comment-user-info">
                                <img src="${comment.author.avatar}" alt="${comment.author.username}" class="comment-avatar">
                                <div class="comment-user-details">
                                    <h4 style="color: ${textColor};">${comment.author.username}</h4>
                                    <div class="comment-time" style="color: ${isLight ? '#666' : '#ccc'};">刚刚</div>
                                </div>
                            </div>
                        </div>
                        <div class="comment-content">
                            <h3 class="comment-title" style="color: ${textColor};">${comment.title}</h3>
                            <p class="comment-text" style="color: ${textColor};">${comment.content}</p>
                        </div>
                        <div class="comment-footer" style="border-top-color: ${isLight ? 'var(--accent-color)' : 'rgba(255,255,255,0.2)'};">
                            <div class="comment-stats">
                                <button class="stat-btn like-btn" style="color: ${isLight ? '#666' : '#ccc'};">
                                    <i class="fas fa-thumbs-up"></i>
                                    <span>${comment.likes}</span>
                                </button>
                                <button class="stat-btn dislike-btn" style="color: ${isLight ? '#666' : '#ccc'};">
                                    <i class="fas fa-thumbs-down"></i>
                                    <span>${comment.dislikes}</span>
                                </button>
                            </div>
                            <button class="comment-reply-btn" style="background: ${isLight ? 'var(--primary-color)' : 'rgba(255,255,255,0.9)'}; color: ${isLight ? 'var(--secondary-color)' : 'var(--primary-color)'};">
                                <i class="fas fa-reply"></i>
                                回复
                            </button>
                        </div>
                    </div>
                `;
            }).join('');
            
            container.innerHTML = commentsHtml;
        }
        
        // 清除测试评论
        function clearTestComments() {
            testComments = [];
            renderTestComments();
            
            const status = document.getElementById('fullTestStatus');
            status.className = 'status info';
            status.innerHTML = '🗑️ 已清除所有测试评论';
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initTestColorPicker();
            testColor('#ffffff'); // 初始化为白色
            
            // 点击遮罩关闭弹窗
            document.getElementById('testCommentModalOverlay').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeFullCommentModal();
                }
            });
            
            console.log('评论框颜色测试页面初始化完成');
        });
    </script>
</body>
</html>
