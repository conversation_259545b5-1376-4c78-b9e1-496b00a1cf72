/**
 * 后台管理系统核心功能
 * 处理用户管理、头衔设置、禁言功能等
 */

class AdminSystem {
    constructor() {
        // 分页配置
        this.pageSize = 10;
        this.currentPage = 1;
        this.totalPages = 1;
        
        // 搜索关键词
        this.searchKeyword = '';
        
        // 当前操作的用户
        this.currentTargetUser = null;
        
        // 初始化
        this.init();
    }
    
    /**
     * 初始化管理系统
     */
    init() {
        // 检查管理员权限
        this.checkAdminPermission();
        
        // 初始化事件监听器
        this.initEventListeners();
        
        // 加载用户列表
        this.loadUserList();
        
        // 更新统计信息
        this.updateStats();
        
        console.log('后台管理系统初始化完成');
    }
    
    /**
     * 检查管理员权限
     */
    checkAdminPermission() {
        // 检查是否已登录
        if (!currentUser) {
            alert('请先登录！');
            window.location.href = '../index.html';
            return;
        }
        
        // 检查是否为管理员
        if (currentUser.role !== 'admin') {
            alert('您没有访问后台管理的权限！');
            window.location.href = '../index.html';
            return;
        }
    }
    
    /**
     * 初始化事件监听器
     */
    initEventListeners() {
        // 搜索功能
        const searchInput = document.getElementById('userSearch');
        const searchBtn = document.getElementById('searchBtn');
        
        searchBtn.addEventListener('click', () => {
            this.searchKeyword = searchInput.value.trim();
            this.currentPage = 1;
            this.loadUserList();
        });
        
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.searchKeyword = searchInput.value.trim();
                this.currentPage = 1;
                this.loadUserList();
            }
        });
        
        // 分页控制
        document.getElementById('prevPage').addEventListener('click', () => {
            if (this.currentPage > 1) {
                this.currentPage--;
                this.loadUserList();
            }
        });
        
        document.getElementById('nextPage').addEventListener('click', () => {
            if (this.currentPage < this.totalPages) {
                this.currentPage++;
                this.loadUserList();
            }
        });
        
        // 头衔设置弹窗
        this.initTitleModal();
        
        // 禁言设置弹窗
        this.initBanModal();
    }
    
    /**
     * 初始化头衔设置弹窗
     */
    initTitleModal() {
        const titleModal = document.getElementById('titleModal');
        const closeTitleModal = document.getElementById('closeTitleModal');
        const titleText = document.getElementById('titleText');
        const titleColor = document.getElementById('titleColor');
        const previewTitle = document.getElementById('previewTitle');
        const previewUsername = document.getElementById('previewUsername');
        const saveTitleBtn = document.getElementById('saveTitleBtn');
        const removeTitleBtn = document.getElementById('removeTitleBtn');
        
        // 关闭弹窗
        closeTitleModal.addEventListener('click', () => {
            this.closeTitleModal();
        });
        
        titleModal.addEventListener('click', (e) => {
            if (e.target === titleModal) {
                this.closeTitleModal();
            }
        });
        
        // 实时预览
        titleText.addEventListener('input', () => {
            this.updateTitlePreview();
        });
        
        titleColor.addEventListener('change', () => {
            this.updateTitlePreview();
        });
        
        // 颜色预设
        document.querySelectorAll('.color-preset').forEach(preset => {
            preset.addEventListener('click', () => {
                titleColor.value = preset.dataset.color;
                this.updateTitlePreview();
            });
        });
        
        // 保存头衔
        saveTitleBtn.addEventListener('click', () => {
            this.saveTitleSettings();
        });
        
        // 移除头衔
        removeTitleBtn.addEventListener('click', () => {
            this.removeTitle();
        });
    }
    
    /**
     * 初始化禁言设置弹窗
     */
    initBanModal() {
        const banModal = document.getElementById('banModal');
        const closeBanModal = document.getElementById('closeBanModal');
        const confirmBanBtn = document.getElementById('confirmBanBtn');
        const unbanBtn = document.getElementById('unbanBtn');
        
        // 关闭弹窗
        closeBanModal.addEventListener('click', () => {
            this.closeBanModal();
        });
        
        banModal.addEventListener('click', (e) => {
            if (e.target === banModal) {
                this.closeBanModal();
            }
        });
        
        // 确认禁言
        confirmBanBtn.addEventListener('click', () => {
            this.confirmBan();
        });
        
        // 解除禁言
        unbanBtn.addEventListener('click', () => {
            this.unbanUser();
        });
    }
    
    /**
     * 加载用户列表
     */
    loadUserList() {
        const userList = document.getElementById('userList');
        
        // 获取过滤后的用户列表
        let filteredUsers = [...users];
        
        // 搜索过滤
        if (this.searchKeyword) {
            const keyword = this.searchKeyword.toLowerCase();
            filteredUsers = filteredUsers.filter(user => 
                user.username.toLowerCase().includes(keyword) ||
                user.uid.toLowerCase().includes(keyword)
            );
        }
        
        // 计算分页
        this.totalPages = Math.ceil(filteredUsers.length / this.pageSize);
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        const pagedUsers = filteredUsers.slice(startIndex, endIndex);
        
        // 生成用户列表HTML
        userList.innerHTML = pagedUsers.map(user => this.generateUserItemHTML(user)).join('');
        
        // 更新分页控制
        this.updatePaginationControls();
        
        // 绑定操作按钮事件
        this.bindUserActionEvents();
    }
    
    /**
     * 生成用户项HTML
     */
    generateUserItemHTML(user) {
        const titleHTML = user.title 
            ? `<span class="user-title" style="background: ${user.titleColor || '#6c5ce7'}">${user.title}</span>`
            : '<span class="no-title">无头衔</span>';
            
        const statusHTML = user.isBanned 
            ? '<span class="status-badge status-banned">已禁言</span>'
            : '<span class="status-badge status-active">正常</span>';
            
        const roleHTML = user.role === 'admin' 
            ? '<span class="role-badge role-admin">管理员</span>'
            : '<span class="role-badge role-user">普通用户</span>';
            
        const actionButtons = user.role !== 'admin' ? `
            <button class="action-btn btn-title" data-action="title" data-uid="${user.uid}">
                <i class="fas fa-crown"></i>
                头衔
            </button>
            ${user.isBanned 
                ? `<button class="action-btn btn-unban" data-action="unban" data-uid="${user.uid}">
                     <i class="fas fa-check"></i>
                     解禁
                   </button>`
                : `<button class="action-btn btn-ban" data-action="ban" data-uid="${user.uid}">
                     <i class="fas fa-ban"></i>
                     禁言
                   </button>`
            }
        ` : '<span class="no-action">管理员账户</span>';
        
        return `
            <div class="user-item">
                <div class="user-cell">
                    <div class="user-info">
                        <img src="${user.avatar}" alt="头像" class="user-avatar-small">
                        <div class="user-details">
                            <h4>${user.username}</h4>
                            <p>${user.uid}</p>
                        </div>
                    </div>
                </div>
                <div class="user-cell">
                    ${roleHTML}
                </div>
                <div class="user-cell">
                    ${titleHTML}
                </div>
                <div class="user-cell">
                    ${statusHTML}
                </div>
                <div class="user-cell">
                    <div class="action-buttons">
                        ${actionButtons}
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * 绑定用户操作事件
     */
    bindUserActionEvents() {
        document.querySelectorAll('.action-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.target.closest('.action-btn').dataset.action;
                const uid = e.target.closest('.action-btn').dataset.uid;
                const user = users.find(u => u.uid === uid);
                
                if (!user) return;
                
                this.currentTargetUser = user;
                
                switch (action) {
                    case 'title':
                        this.openTitleModal(user);
                        break;
                    case 'ban':
                        this.openBanModal(user);
                        break;
                    case 'unban':
                        this.unbanUser();
                        break;
                }
            });
        });
    }
    
    /**
     * 打开头衔设置弹窗
     */
    openTitleModal(user) {
        const titleModal = document.getElementById('titleModal');
        const targetUsername = document.getElementById('targetUsername');
        const titleText = document.getElementById('titleText');
        const titleColor = document.getElementById('titleColor');
        const previewUsername = document.getElementById('previewUsername');
        const titleNotification = document.getElementById('titleNotification');
        
        // 填充用户信息
        targetUsername.value = user.username;
        titleText.value = user.title || '';
        titleColor.value = user.titleColor || '#6c5ce7';
        previewUsername.textContent = user.username;
        
        // 重置通知
        titleNotification.className = 'notification';
        titleNotification.textContent = '';
        
        // 更新预览
        this.updateTitlePreview();
        
        // 显示弹窗
        titleModal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }
    
    /**
     * 关闭头衔设置弹窗
     */
    closeTitleModal() {
        const titleModal = document.getElementById('titleModal');
        titleModal.classList.remove('active');
        document.body.style.overflow = '';
        this.currentTargetUser = null;
    }
    
    /**
     * 更新头衔预览
     */
    updateTitlePreview() {
        const titleText = document.getElementById('titleText');
        const titleColor = document.getElementById('titleColor');
        const previewTitle = document.getElementById('previewTitle');
        
        const title = titleText.value.trim();
        const color = titleColor.value;
        
        if (title) {
            previewTitle.textContent = title;
            previewTitle.style.background = color;
            previewTitle.style.display = 'inline-block';
        } else {
            previewTitle.style.display = 'none';
        }
    }
    
    /**
     * 保存头衔设置
     */
    saveTitleSettings() {
        if (!this.currentTargetUser) return;
        
        const titleText = document.getElementById('titleText');
        const titleColor = document.getElementById('titleColor');
        const titleNotification = document.getElementById('titleNotification');
        
        const title = titleText.value.trim();
        const color = titleColor.value;
        
        // 验证输入
        if (title && title.length > 10) {
            this.showNotification(titleNotification, '头衔名称不能超过10个字符', 'error');
            return;
        }
        
        // 更新用户头衔
        this.currentTargetUser.title = title || null;
        this.currentTargetUser.titleColor = title ? color : null;
        
        // 显示成功消息
        const message = title ? '头衔设置成功！' : '头衔已清除！';
        this.showNotification(titleNotification, message, 'success');
        
        // 更新用户列表和统计
        this.loadUserList();
        this.updateStats();
        
        // 2秒后关闭弹窗
        setTimeout(() => {
            this.closeTitleModal();
        }, 2000);
    }
    
    /**
     * 移除头衔
     */
    removeTitle() {
        if (!this.currentTargetUser) return;
        
        const titleText = document.getElementById('titleText');
        const titleColor = document.getElementById('titleColor');
        const titleNotification = document.getElementById('titleNotification');
        
        // 清空头衔
        this.currentTargetUser.title = null;
        this.currentTargetUser.titleColor = null;
        
        // 清空表单
        titleText.value = '';
        titleColor.value = '#6c5ce7';
        this.updateTitlePreview();
        
        // 显示成功消息
        this.showNotification(titleNotification, '头衔已移除！', 'success');
        
        // 更新用户列表和统计
        this.loadUserList();
        this.updateStats();
        
        // 2秒后关闭弹窗
        setTimeout(() => {
            this.closeTitleModal();
        }, 2000);
    }
    
    /**
     * 打开禁言设置弹窗
     */
    openBanModal(user) {
        const banModal = document.getElementById('banModal');
        const banUsername = document.getElementById('banUsername');
        const banReason = document.getElementById('banReason');
        const banNotification = document.getElementById('banNotification');
        const confirmBanBtn = document.getElementById('confirmBanBtn');
        const unbanBtn = document.getElementById('unbanBtn');
        
        // 填充用户信息
        banUsername.value = user.username;
        banReason.value = user.banReason || '';
        
        // 重置通知
        banNotification.className = 'notification';
        banNotification.textContent = '';
        
        // 根据用户状态显示不同按钮
        if (user.isBanned) {
            confirmBanBtn.style.display = 'none';
            unbanBtn.style.display = 'flex';
            banReason.readOnly = true;
        } else {
            confirmBanBtn.style.display = 'flex';
            unbanBtn.style.display = 'none';
            banReason.readOnly = false;
        }
        
        // 显示弹窗
        banModal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }
    
    /**
     * 关闭禁言设置弹窗
     */
    closeBanModal() {
        const banModal = document.getElementById('banModal');
        banModal.classList.remove('active');
        document.body.style.overflow = '';
        this.currentTargetUser = null;
    }
    
    /**
     * 确认禁言
     */
    confirmBan() {
        if (!this.currentTargetUser) return;
        
        const banReason = document.getElementById('banReason');
        const banNotification = document.getElementById('banNotification');
        
        const reason = banReason.value.trim();
        
        // 验证输入
        if (!reason) {
            this.showNotification(banNotification, '请输入禁言原因', 'error');
            return;
        }
        
        if (reason.length > 200) {
            this.showNotification(banNotification, '禁言原因不能超过200个字符', 'error');
            return;
        }
        
        // 执行禁言
        this.currentTargetUser.isBanned = true;
        this.currentTargetUser.banReason = reason;
        
        // 显示成功消息
        this.showNotification(banNotification, '用户已被禁言！', 'success');
        
        // 更新用户列表和统计
        this.loadUserList();
        this.updateStats();
        
        // 2秒后关闭弹窗
        setTimeout(() => {
            this.closeBanModal();
        }, 2000);
    }
    
    /**
     * 解除禁言
     */
    unbanUser() {
        if (!this.currentTargetUser) return;
        
        const banNotification = document.getElementById('banNotification');
        
        // 解除禁言
        this.currentTargetUser.isBanned = false;
        this.currentTargetUser.banReason = null;
        
        // 显示成功消息
        this.showNotification(banNotification, '用户禁言已解除！', 'success');
        
        // 更新用户列表和统计
        this.loadUserList();
        this.updateStats();
        
        // 2秒后关闭弹窗
        setTimeout(() => {
            this.closeBanModal();
        }, 2000);
    }
    
    /**
     * 更新分页控制
     */
    updatePaginationControls() {
        const prevBtn = document.getElementById('prevPage');
        const nextBtn = document.getElementById('nextPage');
        const pageInfo = document.getElementById('pageInfo');
        
        // 更新按钮状态
        prevBtn.disabled = this.currentPage <= 1;
        nextBtn.disabled = this.currentPage >= this.totalPages;
        
        // 更新页面信息
        pageInfo.textContent = `第 ${this.currentPage} 页，共 ${this.totalPages} 页`;
    }
    
    /**
     * 更新统计信息
     */
    updateStats() {
        const totalUsers = users.length;
        const titledUsers = users.filter(u => u.title).length;
        const bannedUsers = users.filter(u => u.isBanned).length;
        const adminUsers = users.filter(u => u.role === 'admin').length;
        
        document.getElementById('totalUsers').textContent = totalUsers;
        document.getElementById('titledUsers').textContent = titledUsers;
        document.getElementById('bannedUsers').textContent = bannedUsers;
        document.getElementById('adminUsers').textContent = adminUsers;
    }
    
    /**
     * 显示通知消息
     */
    showNotification(element, message, type) {
        element.textContent = message;
        element.className = `notification ${type}`;
    }
}

// 等待DOM加载完成后初始化管理系统
document.addEventListener('DOMContentLoaded', function() {
    // 确保用户数据已加载
    if (typeof users !== 'undefined' && typeof currentUser !== 'undefined') {
        window.adminSystem = new AdminSystem();
    } else {
        console.error('用户数据未加载，无法初始化管理系统');
    }
});

// 监听用户登录/退出事件
window.addEventListener('userLoggedIn', function(e) {
    // 重新检查权限
    if (window.adminSystem) {
        window.adminSystem.checkAdminPermission();
        window.adminSystem.loadUserList();
        window.adminSystem.updateStats();
    }
});

window.addEventListener('userLoggedOut', function() {
    // 用户退出登录，跳转到首页
    window.location.href = '../index.html';
});
