<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>后台管理测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #5e35b1;
        }
        .test-btn {
            background: #5e35b1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-btn:hover {
            background: #4527a0;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            background: #f0f0f0;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>后台管理系统功能测试</h1>
        
        <div class="test-section">
            <h3>1. 用户数据测试</h3>
            <button class="test-btn" onclick="testUserData()">测试用户数据</button>
            <div id="userDataResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>2. 管理员权限测试</h3>
            <button class="test-btn" onclick="testAdminLogin()">模拟管理员登录</button>
            <button class="test-btn" onclick="testUserLogin()">模拟普通用户登录</button>
            <button class="test-btn" onclick="testLogout()">退出登录</button>
            <div id="adminResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>3. 头衔功能测试</h3>
            <button class="test-btn" onclick="testSetTitle()">设置用户头衔</button>
            <button class="test-btn" onclick="testGetTitle()">获取用户头衔</button>
            <button class="test-btn" onclick="testRemoveTitle()">移除用户头衔</button>
            <div id="titleResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>4. 禁言功能测试</h3>
            <button class="test-btn" onclick="testBanUser()">禁言用户</button>
            <button class="test-btn" onclick="testUnbanUser()">解除禁言</button>
            <button class="test-btn" onclick="testCheckBan()">检查禁言状态</button>
            <div id="banResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>5. 页面跳转测试</h3>
            <button class="test-btn" onclick="goToAdmin()">进入后台管理</button>
            <button class="test-btn" onclick="goToReview()">进入校评页面</button>
            <button class="test-btn" onclick="goToHome()">返回首页</button>
            <div id="navigationResult" class="test-result"></div>
        </div>
    </div>

    <!-- 引入主要的JavaScript文件 -->
    <script src="js/main.js"></script>
    
    <script>
        // 测试用户数据
        function testUserData() {
            const result = document.getElementById('userDataResult');
            try {
                if (typeof users !== 'undefined') {
                    result.innerHTML = `
                        <div class="success">
                            <strong>用户数据加载成功！</strong><br>
                            总用户数: ${users.length}<br>
                            管理员数: ${users.filter(u => u.role === 'admin').length}<br>
                            有头衔用户: ${users.filter(u => u.title).length}<br>
                            被禁言用户: ${users.filter(u => u.isBanned).length}
                        </div>
                    `;
                } else {
                    result.innerHTML = '<div class="error">用户数据未加载</div>';
                }
            } catch (error) {
                result.innerHTML = `<div class="error">测试失败: ${error.message}</div>`;
            }
        }

        // 测试管理员登录
        function testAdminLogin() {
            const result = document.getElementById('adminResult');
            try {
                // 模拟管理员登录
                const adminUser = users.find(u => u.role === 'admin');
                if (adminUser) {
                    window.currentUser = adminUser;
                    localStorage.setItem('currentUser', JSON.stringify(adminUser));
                    
                    // 触发登录事件
                    window.dispatchEvent(new CustomEvent('userLoggedIn', {
                        detail: { user: adminUser }
                    }));
                    
                    result.innerHTML = `
                        <div class="success">
                            管理员登录成功！<br>
                            用户名: ${adminUser.username}<br>
                            角色: ${adminUser.role}<br>
                            UID: ${adminUser.uid}
                        </div>
                    `;
                } else {
                    result.innerHTML = '<div class="error">未找到管理员账户</div>';
                }
            } catch (error) {
                result.innerHTML = `<div class="error">登录失败: ${error.message}</div>`;
            }
        }

        // 测试普通用户登录
        function testUserLogin() {
            const result = document.getElementById('adminResult');
            try {
                // 模拟普通用户登录
                const normalUser = users.find(u => u.role === 'user');
                if (normalUser) {
                    window.currentUser = normalUser;
                    localStorage.setItem('currentUser', JSON.stringify(normalUser));
                    
                    // 触发登录事件
                    window.dispatchEvent(new CustomEvent('userLoggedIn', {
                        detail: { user: normalUser }
                    }));
                    
                    result.innerHTML = `
                        <div class="info">
                            普通用户登录成功！<br>
                            用户名: ${normalUser.username}<br>
                            角色: ${normalUser.role}<br>
                            UID: ${normalUser.uid}
                        </div>
                    `;
                } else {
                    result.innerHTML = '<div class="error">未找到普通用户账户</div>';
                }
            } catch (error) {
                result.innerHTML = `<div class="error">登录失败: ${error.message}</div>`;
            }
        }

        // 测试退出登录
        function testLogout() {
            const result = document.getElementById('adminResult');
            try {
                window.currentUser = null;
                localStorage.removeItem('currentUser');
                
                // 触发退出登录事件
                window.dispatchEvent(new CustomEvent('userLoggedOut'));
                
                result.innerHTML = '<div class="info">已退出登录</div>';
            } catch (error) {
                result.innerHTML = `<div class="error">退出失败: ${error.message}</div>`;
            }
        }

        // 测试设置头衔
        function testSetTitle() {
            const result = document.getElementById('titleResult');
            try {
                const testUser = users.find(u => u.role === 'user');
                if (testUser) {
                    testUser.title = '测试头衔';
                    testUser.titleColor = '#e74c3c';
                    
                    result.innerHTML = `
                        <div class="success">
                            头衔设置成功！<br>
                            用户: ${testUser.username}<br>
                            头衔: <span style="background: ${testUser.titleColor}; color: white; padding: 2px 8px; border-radius: 10px; font-size: 0.8rem;">${testUser.title}</span>
                        </div>
                    `;
                } else {
                    result.innerHTML = '<div class="error">未找到测试用户</div>';
                }
            } catch (error) {
                result.innerHTML = `<div class="error">设置失败: ${error.message}</div>`;
            }
        }

        // 测试获取头衔
        function testGetTitle() {
            const result = document.getElementById('titleResult');
            try {
                if (typeof getUserTitle === 'function') {
                    const testUser = users.find(u => u.title);
                    if (testUser) {
                        const titleInfo = getUserTitle(testUser.uid);
                        if (titleInfo) {
                            result.innerHTML = `
                                <div class="success">
                                    头衔获取成功！<br>
                                    用户: ${testUser.username}<br>
                                    头衔: <span style="background: ${titleInfo.color}; color: white; padding: 2px 8px; border-radius: 10px; font-size: 0.8rem;">${titleInfo.title}</span>
                                </div>
                            `;
                        } else {
                            result.innerHTML = '<div class="info">该用户没有头衔</div>';
                        }
                    } else {
                        result.innerHTML = '<div class="info">没有用户设置了头衔</div>';
                    }
                } else {
                    result.innerHTML = '<div class="error">getUserTitle 函数未定义</div>';
                }
            } catch (error) {
                result.innerHTML = `<div class="error">获取失败: ${error.message}</div>`;
            }
        }

        // 测试移除头衔
        function testRemoveTitle() {
            const result = document.getElementById('titleResult');
            try {
                const testUser = users.find(u => u.title);
                if (testUser) {
                    const oldTitle = testUser.title;
                    testUser.title = null;
                    testUser.titleColor = null;
                    
                    result.innerHTML = `
                        <div class="success">
                            头衔移除成功！<br>
                            用户: ${testUser.username}<br>
                            已移除头衔: ${oldTitle}
                        </div>
                    `;
                } else {
                    result.innerHTML = '<div class="info">没有用户设置了头衔</div>';
                }
            } catch (error) {
                result.innerHTML = `<div class="error">移除失败: ${error.message}</div>`;
            }
        }

        // 测试禁言用户
        function testBanUser() {
            const result = document.getElementById('banResult');
            try {
                const testUser = users.find(u => u.role === 'user');
                if (testUser) {
                    testUser.isBanned = true;
                    testUser.banReason = '测试禁言功能';
                    
                    result.innerHTML = `
                        <div class="success">
                            用户禁言成功！<br>
                            用户: ${testUser.username}<br>
                            禁言原因: ${testUser.banReason}
                        </div>
                    `;
                } else {
                    result.innerHTML = '<div class="error">未找到测试用户</div>';
                }
            } catch (error) {
                result.innerHTML = `<div class="error">禁言失败: ${error.message}</div>`;
            }
        }

        // 测试解除禁言
        function testUnbanUser() {
            const result = document.getElementById('banResult');
            try {
                const testUser = users.find(u => u.isBanned);
                if (testUser) {
                    testUser.isBanned = false;
                    testUser.banReason = null;
                    
                    result.innerHTML = `
                        <div class="success">
                            用户解除禁言成功！<br>
                            用户: ${testUser.username}
                        </div>
                    `;
                } else {
                    result.innerHTML = '<div class="info">没有被禁言的用户</div>';
                }
            } catch (error) {
                result.innerHTML = `<div class="error">解除禁言失败: ${error.message}</div>`;
            }
        }

        // 测试检查禁言状态
        function testCheckBan() {
            const result = document.getElementById('banResult');
            try {
                if (typeof isUserBanned === 'function') {
                    const testUser = users.find(u => u.role === 'user');
                    if (testUser) {
                        const isBanned = isUserBanned(testUser.uid);
                        result.innerHTML = `
                            <div class="${isBanned ? 'error' : 'success'}">
                                禁言状态检查完成！<br>
                                用户: ${testUser.username}<br>
                                状态: ${isBanned ? '已禁言' : '正常'}
                            </div>
                        `;
                    } else {
                        result.innerHTML = '<div class="error">未找到测试用户</div>';
                    }
                } else {
                    result.innerHTML = '<div class="error">isUserBanned 函数未定义</div>';
                }
            } catch (error) {
                result.innerHTML = `<div class="error">检查失败: ${error.message}</div>`;
            }
        }

        // 页面跳转测试
        function goToAdmin() {
            const result = document.getElementById('navigationResult');
            if (window.currentUser && window.currentUser.role === 'admin') {
                result.innerHTML = '<div class="success">正在跳转到后台管理...</div>';
                setTimeout(() => {
                    window.location.href = 'admin/index.html';
                }, 1000);
            } else {
                result.innerHTML = '<div class="error">需要管理员权限才能访问后台管理</div>';
            }
        }

        function goToReview() {
            const result = document.getElementById('navigationResult');
            result.innerHTML = '<div class="info">正在跳转到校评页面...</div>';
            setTimeout(() => {
                window.location.href = 'school-review/index.html';
            }, 1000);
        }

        function goToHome() {
            const result = document.getElementById('navigationResult');
            result.innerHTML = '<div class="info">正在跳转到首页...</div>';
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 1000);
        }

        // 页面加载完成后自动测试用户数据
        window.addEventListener('DOMContentLoaded', function() {
            setTimeout(testUserData, 500);
        });
    </script>
</body>
</html>
