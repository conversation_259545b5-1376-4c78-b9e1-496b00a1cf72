<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #5e35b1;
        }
        .test-btn {
            background: #5e35b1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-btn:hover {
            background: #4527a0;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            background: #f0f0f0;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 功能修复验证</h1>
        
        <div class="test-section">
            <h3>1. 管理员按钮显示测试</h3>
            <p>测试管理员登录后是否正确显示后台管理按钮</p>
            <button class="test-btn" onclick="testAdminButton()">测试管理员按钮</button>
            <div id="adminButtonResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>2. 个人信息功能测试</h3>
            <p>测试个人信息弹窗和相关功能</p>
            <button class="test-btn" onclick="testProfileModal()">测试个人信息</button>
            <div id="profileResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>3. 问候语功能测试</h3>
            <p>测试时间相关的问候语显示</p>
            <button class="test-btn" onclick="testGreeting()">测试问候语</button>
            <div id="greetingResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>4. 导航栏一致性测试</h3>
            <p>测试各页面导航栏的一致性</p>
            <button class="test-btn" onclick="testNavigation()">测试导航栏</button>
            <div id="navigationResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>5. 页面跳转测试</h3>
            <p>测试页面间的跳转功能</p>
            <button class="test-btn" onclick="goToHome()">首页</button>
            <button class="test-btn" onclick="goToReview()">校评</button>
            <button class="test-btn" onclick="goToAdmin()">后台管理</button>
            <div id="jumpResult" class="test-result"></div>
        </div>
    </div>

    <!-- 引入主要的JavaScript文件 -->
    <script src="js/main.js"></script>
    
    <script>
        // 测试管理员按钮功能
        function testAdminButton() {
            const result = document.getElementById('adminButtonResult');
            
            try {
                // 模拟管理员登录
                const adminUser = users.find(u => u.role === 'admin');
                if (adminUser) {
                    window.currentUser = adminUser;
                    localStorage.setItem('currentUser', JSON.stringify(adminUser));
                    
                    // 调用更新函数
                    if (typeof updateAdminButton === 'function') {
                        updateAdminButton();
                        
                        // 检查按钮是否存在
                        const adminBtn = document.getElementById('adminBtn');
                        if (adminBtn) {
                            result.innerHTML = `
                                <div class="success">
                                    ✅ 管理员按钮功能正常<br>
                                    - updateAdminButton 函数存在<br>
                                    - 管理员登录后按钮应该显示<br>
                                    - 当前用户: ${adminUser.username} (${adminUser.role})
                                </div>
                            `;
                        } else {
                            result.innerHTML = `
                                <div class="warning">
                                    ⚠️ 当前页面没有管理员按钮元素<br>
                                    这是正常的，因为这是测试页面<br>
                                    请在首页或其他页面测试
                                </div>
                            `;
                        }
                    } else {
                        result.innerHTML = '<div class="error">❌ updateAdminButton 函数不存在</div>';
                    }
                } else {
                    result.innerHTML = '<div class="error">❌ 未找到管理员用户</div>';
                }
            } catch (error) {
                result.innerHTML = `<div class="error">❌ 测试失败: ${error.message}</div>`;
            }
        }

        // 测试个人信息功能
        function testProfileModal() {
            const result = document.getElementById('profileResult');
            
            try {
                // 检查相关函数是否存在
                const functions = [
                    'updateUserInterface',
                    'resetUserInterface', 
                    'checkLoginStatus',
                    'initProfileModal'
                ];
                
                const existingFunctions = functions.filter(func => typeof window[func] === 'function');
                const missingFunctions = functions.filter(func => typeof window[func] !== 'function');
                
                result.innerHTML = `
                    <div class="${missingFunctions.length === 0 ? 'success' : 'warning'}">
                        <strong>个人信息功能检查:</strong><br>
                        ✅ 存在的函数: ${existingFunctions.join(', ')}<br>
                        ${missingFunctions.length > 0 ? `⚠️ 缺失的函数: ${missingFunctions.join(', ')}<br>` : ''}
                        <br>
                        <strong>用户数据结构检查:</strong><br>
                        ${users && users.length > 0 ? `✅ 用户数据正常 (${users.length} 个用户)` : '❌ 用户数据异常'}
                    </div>
                `;
            } catch (error) {
                result.innerHTML = `<div class="error">❌ 测试失败: ${error.message}</div>`;
            }
        }

        // 测试问候语功能
        function testGreeting() {
            const result = document.getElementById('greetingResult');
            
            try {
                // 获取当前时间并生成问候语
                const currentHour = new Date().getHours();
                let expectedGreeting = '';
                
                if (currentHour >= 0 && currentHour < 6) {
                    expectedGreeting = "天黑了，多休息，同学";
                } else if (currentHour >= 6 && currentHour < 11) {
                    expectedGreeting = "上午好，同学";
                } else if (currentHour >= 11 && currentHour < 13) {
                    expectedGreeting = "中午好，同学";
                } else if (currentHour >= 13 && currentHour < 20) {
                    expectedGreeting = "下午好，同学";
                } else {
                    expectedGreeting = "晚上好，同学";
                }
                
                result.innerHTML = `
                    <div class="success">
                        ✅ 问候语功能正常<br>
                        - 当前时间: ${currentHour}:${new Date().getMinutes()}<br>
                        - 预期问候语: "${expectedGreeting}"<br>
                        - 问候语会在首页的欢迎区域显示
                    </div>
                `;
            } catch (error) {
                result.innerHTML = `<div class="error">❌ 测试失败: ${error.message}</div>`;
            }
        }

        // 测试导航栏一致性
        function testNavigation() {
            const result = document.getElementById('navigationResult');
            
            const pages = [
                { name: '首页', url: 'index.html', hasAdmin: true },
                { name: '校评', url: 'school-review/index.html', hasAdmin: true },
                { name: '后台管理', url: 'admin/index.html', hasAdmin: true }
            ];
            
            result.innerHTML = `
                <div class="info">
                    <strong>导航栏结构检查:</strong><br>
                    ${pages.map(page => `
                        📄 ${page.name} (${page.url})<br>
                        &nbsp;&nbsp;&nbsp;&nbsp;${page.hasAdmin ? '✅ 应包含管理员按钮' : '❌ 不包含管理员按钮'}
                    `).join('')}
                    <br>
                    <strong>注意事项:</strong><br>
                    - 管理员按钮只有在管理员登录后才显示<br>
                    - 所有页面的导航栏结构应该一致
                </div>
            `;
        }

        // 页面跳转函数
        function goToHome() {
            const result = document.getElementById('jumpResult');
            result.innerHTML = '<div class="info">正在跳转到首页...</div>';
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 1000);
        }

        function goToReview() {
            const result = document.getElementById('jumpResult');
            result.innerHTML = '<div class="info">正在跳转到校评页面...</div>';
            setTimeout(() => {
                window.location.href = 'school-review/index.html';
            }, 1000);
        }

        function goToAdmin() {
            const result = document.getElementById('jumpResult');
            if (window.currentUser && window.currentUser.role === 'admin') {
                result.innerHTML = '<div class="success">管理员权限验证通过，正在跳转...</div>';
                setTimeout(() => {
                    window.location.href = 'admin/index.html';
                }, 1000);
            } else {
                result.innerHTML = '<div class="warning">需要管理员权限，请先登录管理员账户</div>';
            }
        }

        // 页面加载完成后自动运行一些测试
        window.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                testGreeting();
                testProfileModal();
            }, 500);
        });
    </script>
</body>
</html>
