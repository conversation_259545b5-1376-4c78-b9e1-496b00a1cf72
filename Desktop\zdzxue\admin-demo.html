<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>后台管理系统演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .demo-header {
            background: linear-gradient(135deg, #5e35b1, #7e57c2);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .demo-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .demo-header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .demo-content {
            padding: 40px;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .feature-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            border-left: 5px solid #5e35b1;
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .feature-card h3 {
            color: #5e35b1;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .feature-card ul {
            list-style: none;
        }

        .feature-card li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }

        .feature-card li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #27ae60;
            font-weight: bold;
        }

        .demo-section {
            margin-bottom: 40px;
        }

        .demo-section h2 {
            color: #5e35b1;
            margin-bottom: 20px;
            font-size: 1.8rem;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }

        .demo-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 30px;
        }

        .demo-btn {
            background: linear-gradient(135deg, #5e35b1, #7e57c2);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(94, 53, 177, 0.3);
        }

        .demo-btn.secondary {
            background: linear-gradient(135deg, #6c757d, #495057);
        }

        .demo-btn.success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .demo-btn.warning {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
        }

        .demo-btn.danger {
            background: linear-gradient(135deg, #dc3545, #e83e8c);
        }

        .screenshot-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }

        .screenshot-placeholder {
            background: linear-gradient(45deg, #e9ecef, #dee2e6);
            border: 2px dashed #adb5bd;
            border-radius: 10px;
            padding: 40px;
            color: #6c757d;
            font-size: 1.1rem;
        }

        .info-box {
            background: linear-gradient(135deg, #d1ecf1, #bee5eb);
            border: 1px solid #bee5eb;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            color: #0c5460;
        }

        .info-box h4 {
            margin-bottom: 10px;
            color: #0c5460;
        }

        .warning-box {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            color: #856404;
        }

        .warning-box h4 {
            margin-bottom: 10px;
            color: #856404;
        }

        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 20px 0;
        }

        .footer {
            background: #f8f9fa;
            padding: 30px;
            text-align: center;
            color: #6c757d;
            border-top: 1px solid #e9ecef;
        }

        @media (max-width: 768px) {
            .demo-header {
                padding: 20px;
            }

            .demo-header h1 {
                font-size: 2rem;
            }

            .demo-content {
                padding: 20px;
            }

            .feature-grid {
                grid-template-columns: 1fr;
            }

            .demo-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <!-- 头部 -->
        <div class="demo-header">
            <h1>🛠️ 后台管理系统演示</h1>
            <p>重庆市梁平区知德中学 - 用户管理与权限控制系统</p>
        </div>

        <!-- 主要内容 -->
        <div class="demo-content">
            <!-- 功能特色 -->
            <div class="demo-section">
                <h2>🌟 核心功能</h2>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h3>👥 用户管理</h3>
                        <ul>
                            <li>用户列表查看</li>
                            <li>用户搜索功能</li>
                            <li>分页浏览支持</li>
                            <li>用户信息展示</li>
                            <li>角色权限管理</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h3>👑 头衔系统</h3>
                        <ul>
                            <li>自定义头衔名称</li>
                            <li>头衔颜色选择</li>
                            <li>实时预览效果</li>
                            <li>评论中显示头衔</li>
                            <li>用户可选择显示</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h3>🚫 禁言功能</h3>
                        <ul>
                            <li>用户禁言管理</li>
                            <li>禁言原因记录</li>
                            <li>评论功能限制</li>
                            <li>界面状态提示</li>
                            <li>一键解除禁言</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h3>🔒 权限控制</h3>
                        <ul>
                            <li>管理员专用访问</li>
                            <li>权限验证机制</li>
                            <li>安全操作保护</li>
                            <li>多层权限检查</li>
                            <li>操作日志记录</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 快速体验 -->
            <div class="demo-section">
                <h2>🚀 快速体验</h2>
                <div class="demo-buttons">
                    <a href="index.html" class="demo-btn">
                        🏠 返回首页
                    </a>
                    <a href="test-admin.html" class="demo-btn secondary">
                        🧪 功能测试
                    </a>
                    <a href="admin/index.html" class="demo-btn success">
                        ⚙️ 进入后台管理
                    </a>
                    <a href="school-review/index.html" class="demo-btn warning">
                        💬 校评页面
                    </a>
                </div>

                <div class="info-box">
                    <h4>💡 使用提示</h4>
                    <p>
                        <strong>管理员账户：</strong> admin / admin123<br>
                        <strong>普通用户：</strong> test / test123<br>
                        请先登录管理员账户才能访问后台管理系统。
                    </p>
                </div>
            </div>

            <!-- 功能演示 -->
            <div class="demo-section">
                <h2>📱 界面预览</h2>
                <div class="screenshot-container">
                    <div class="screenshot-placeholder">
                        🖼️ 后台管理系统界面截图<br>
                        <small>包含用户列表、搜索功能、统计信息等</small>
                    </div>
                </div>
                <div class="screenshot-container">
                    <div class="screenshot-placeholder">
                        🎨 头衔设置界面<br>
                        <small>支持自定义头衔名称和颜色选择</small>
                    </div>
                </div>
                <div class="screenshot-container">
                    <div class="screenshot-placeholder">
                        🚫 禁言管理界面<br>
                        <small>禁言原因设置和状态管理</small>
                    </div>
                </div>
            </div>

            <!-- 技术实现 -->
            <div class="demo-section">
                <h2>⚡ 技术特色</h2>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h3>🎨 现代化设计</h3>
                        <ul>
                            <li>紫色主题风格</li>
                            <li>渐变色彩搭配</li>
                            <li>流畅动画效果</li>
                            <li>响应式布局</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h3>📱 移动端适配</h3>
                        <ul>
                            <li>完全响应式设计</li>
                            <li>触摸友好交互</li>
                            <li>自适应屏幕尺寸</li>
                            <li>优化移动体验</li>
                        </ul>
                    </div>
                </div>

                <div class="code-block">
// 核心功能代码示例
class AdminSystem {
    constructor() {
        this.pageSize = 10;
        this.currentPage = 1;
        this.searchKeyword = '';
        this.init();
    }
    
    // 用户搜索功能
    searchUsers(keyword) {
        return users.filter(user => 
            user.username.toLowerCase().includes(keyword.toLowerCase())
        );
    }
    
    // 设置用户头衔
    setUserTitle(uid, title, color) {
        const user = users.find(u => u.uid === uid);
        if (user) {
            user.title = title;
            user.titleColor = color;
            return { success: true };
        }
        return { success: false };
    }
}
                </div>
            </div>

            <!-- 使用说明 -->
            <div class="demo-section">
                <h2>📖 使用说明</h2>
                
                <div class="warning-box">
                    <h4>⚠️ 重要提醒</h4>
                    <p>
                        1. 只有管理员账户才能访问后台管理系统<br>
                        2. 请妥善保管管理员账户信息<br>
                        3. 禁言等操作请谨慎使用，避免影响用户体验<br>
                        4. 建议定期备份用户数据
                    </p>
                </div>

                <h3>🔑 登录步骤</h3>
                <ol style="padding-left: 20px; line-height: 1.8;">
                    <li>在首页点击"登录"按钮</li>
                    <li>输入管理员账户：admin / admin123</li>
                    <li>登录成功后，导航栏中央会出现"后台管理"按钮</li>
                    <li>点击按钮进入后台管理系统</li>
                </ol>

                <h3>👥 用户管理</h3>
                <ol style="padding-left: 20px; line-height: 1.8;">
                    <li>查看用户列表：显示所有注册用户信息</li>
                    <li>搜索用户：在搜索框输入用户名进行搜索</li>
                    <li>分页浏览：支持分页查看，每页显示10个用户</li>
                    <li>查看统计：顶部显示用户统计信息</li>
                </ol>

                <h3>👑 头衔设置</h3>
                <ol style="padding-left: 20px; line-height: 1.8;">
                    <li>点击用户操作栏的"头衔"按钮</li>
                    <li>输入头衔名称（最多10个字符）</li>
                    <li>选择头衔颜色（预设颜色或自定义）</li>
                    <li>预览效果后点击"保存头衔"</li>
                    <li>用户在评论时会显示设置的头衔</li>
                </ol>

                <h3>🚫 禁言管理</h3>
                <ol style="padding-left: 20px; line-height: 1.8;">
                    <li>点击用户操作栏的"禁言"按钮</li>
                    <li>输入禁言原因（最多200个字符）</li>
                    <li>点击"确认禁言"执行禁言</li>
                    <li>被禁言用户无法发表评论和回复</li>
                    <li>可随时点击"解禁"按钮解除禁言</li>
                </ol>
            </div>
        </div>

        <!-- 页脚 -->
        <div class="footer">
            <p>© 2024 重庆市梁平区知德中学 - 后台管理系统</p>
            <p>技术支持：现代化Web技术栈 | 设计理念：简洁美观，功能完善</p>
        </div>
    </div>
</body>
</html>
