/**
 * 校评页面主要逻辑（第一部分）
 * 处理页面初始化、用户交互、弹窗管理等
 */

class SchoolReviewApp {
    constructor() {
        // 核心组件
        this.commentSystem = new CommentSystem();
        this.imageUploader = new ImageUploader();

        // DOM元素
        this.elements = {};

        // 状态管理
        this.state = {
            isLoggedIn: false,
            currentUser: null,
            selectedImages: [],
            selectedColor: '#ffffff',
            isUploading: false,
            currentReplyTo: null
        };

        // 颜色选择器实例
        this.colorPicker = null;

        // 初始化
        this.init();
    }

    /**
     * 初始化应用
     */
    init() {
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initApp());
        } else {
            this.initApp();
        }
    }

    /**
     * 初始化应用主体
     */
    initApp() {
        // 获取DOM元素
        this.initElements();

        // 检查登录状态
        this.checkLoginStatus();

        // 绑定事件监听器
        this.bindEventListeners();

        // 初始化颜色选择器
        this.initColorPicker();

        // 加载评论列表
        this.loadComments();

        // 加载草稿
        this.loadDraft();

        console.log('校评页面初始化完成');
    }

    /**
     * 获取DOM元素
     */
    initElements() {
        this.elements = {
            // 搜索和排序
            searchInput: document.getElementById('searchInput'),
            searchBtn: document.getElementById('searchBtn'),
            sortSelect: document.getElementById('sortSelect'),

            // 悬浮添加评论按钮
            floatingAddBtn: document.getElementById('floatingAddBtn'),

            // 评论列表
            commentsContainer: document.getElementById('commentsContainer'),
            loadingIndicator: document.getElementById('loadingIndicator'),
            loadMoreContainer: document.getElementById('loadMoreContainer'),
            loadMoreBtn: document.getElementById('loadMoreBtn'),

            // 评论弹窗
            commentModalOverlay: document.getElementById('commentModalOverlay'),
            commentModal: document.getElementById('commentModal'),
            commentModalClose: document.getElementById('commentModalClose'),
            commentForm: document.getElementById('commentForm'),
            commentTitle: document.getElementById('commentTitle'),
            commentContent: document.getElementById('commentContent'),
            titleCharCount: document.getElementById('titleCharCount'),
            contentCharCount: document.getElementById('contentCharCount'),

            // 图片上传
            uploadImageBtn: document.getElementById('uploadImageBtn'),
            imageInput: document.getElementById('imageInput'),
            imagePreviewContainer: document.getElementById('imagePreviewContainer'),
            imagePreviewList: document.getElementById('imagePreviewList'),

            // 颜色选择
            colorPickerBtn: document.getElementById('colorPickerBtn'),
            colorPickerContainer: document.getElementById('colorPickerContainer'),
            colorPicker: document.getElementById('colorPicker'),
            colorPickerClose: document.getElementById('colorPickerClose'),
            colorPreviewBox: document.getElementById('colorPreviewBox'),

            // 表单按钮
            cancelCommentBtn: document.getElementById('cancelCommentBtn'),
            submitCommentBtn: document.getElementById('submitCommentBtn'),

            // 回复弹窗
            replyModalOverlay: document.getElementById('replyModalOverlay'),
            replyModal: document.getElementById('replyModal'),
            replyModalClose: document.getElementById('replyModalClose'),
            replyForm: document.getElementById('replyForm'),
            replyToInfo: document.getElementById('replyToInfo'),
            replyContent: document.getElementById('replyContent'),
            replyCharCount: document.getElementById('replyCharCount'),
            cancelReplyBtn: document.getElementById('cancelReplyBtn'),
            submitReplyBtn: document.getElementById('submitReplyBtn'),

            // 用户相关
            loginBtn: document.getElementById('loginBtn'),
            avatarContainer: document.getElementById('avatarContainer'),
            userAvatar: document.getElementById('userAvatar'),

            // 登录弹窗
            loginModalOverlay: document.getElementById('loginModalOverlay'),
            closeModal: document.getElementById('closeModal'),
            loginTab: document.getElementById('loginTab'),
            registerTab: document.getElementById('registerTab'),
            loginForm: document.getElementById('loginForm'),
            registerForm: document.getElementById('registerForm'),
            loginButton: document.getElementById('loginButton'),
            registerButton: document.getElementById('registerButton'),
            switchToRegister: document.getElementById('switchToRegister'),
            switchToLogin: document.getElementById('switchToLogin'),

            // 个人信息弹窗
            profileModalOverlay: document.getElementById('profileModalOverlay'),
            closeProfileModal: document.getElementById('closeProfileModal'),
            saveProfileButton: document.getElementById('saveProfileButton'),
            logoutButton: document.getElementById('logoutButton')
        };
    }

    /**
     * 检查登录状态
     */
    checkLoginStatus() {
        // 从localStorage获取登录状态
        try {
            const storedUser = localStorage.getItem('currentUser');
            if (storedUser) {
                const user = JSON.parse(storedUser);
                this.state.isLoggedIn = true;
                this.state.currentUser = user;
                this.commentSystem.setCurrentUser(user);
                this.updateUserInterface();
                this.updateAdminButton();
                this.updateFloatingButton();
            }
        } catch (error) {
            console.error('读取登录状态失败:', error);
        }

        // 也检查主页的登录系统
        if (window.currentUser) {
            this.state.isLoggedIn = true;
            this.state.currentUser = window.currentUser;
            this.commentSystem.setCurrentUser(window.currentUser);
            this.updateUserInterface();
            this.updateAdminButton();
            this.updateFloatingButton();
        }
    }

    /**
     * 更新用户界面
     */
    updateUserInterface() {
        if (this.state.isLoggedIn && this.state.currentUser) {
            // 显示头像，隐藏登录按钮
            if (this.elements.avatarContainer) {
                this.elements.avatarContainer.style.display = 'block';
            }
            if (this.elements.loginBtn) {
                this.elements.loginBtn.style.display = 'none';
            }
            if (this.elements.userAvatar) {
                this.elements.userAvatar.src = this.state.currentUser.avatar;
            }
        } else {
            // 隐藏头像，显示登录按钮
            if (this.elements.avatarContainer) {
                this.elements.avatarContainer.style.display = 'none';
            }
            if (this.elements.loginBtn) {
                this.elements.loginBtn.style.display = 'block';
            }
        }
    }

    /**
     * 绑定事件监听器
     */
    bindEventListeners() {
        // 搜索功能
        if (this.elements.searchBtn) {
            this.elements.searchBtn.addEventListener('click', () => this.handleSearch());
        }
        if (this.elements.searchInput) {
            this.elements.searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.handleSearch();
                }
            });
        }

        // 排序功能
        if (this.elements.sortSelect) {
            this.elements.sortSelect.addEventListener('change', (e) => {
                this.handleSortChange(e.target.value);
            });
        }

        // 悬浮添加评论按钮
        if (this.elements.floatingAddBtn) {
            this.elements.floatingAddBtn.addEventListener('click', () => this.openCommentModal());
        }

        // 加载更多
        if (this.elements.loadMoreBtn) {
            this.elements.loadMoreBtn.addEventListener('click', () => this.loadMoreComments());
        }

        // 评论弹窗
        this.bindCommentModalEvents();

        // 回复弹窗
        this.bindReplyModalEvents();

        // 登录相关
        if (this.elements.loginBtn) {
            this.elements.loginBtn.addEventListener('click', () => this.openLoginModal());
        }

        // 绑定登录弹窗事件
        this.bindLoginModalEvents();

        // 监听主页登录状态变化
        window.addEventListener('userLoggedIn', (e) => {
            this.state.isLoggedIn = true;
            this.state.currentUser = e.detail.user;
            this.commentSystem.setCurrentUser(e.detail.user);
            this.updateUserInterface();
            this.updateAdminButton();
            this.updateFloatingButton();
        });

        window.addEventListener('userLoggedOut', () => {
            this.state.isLoggedIn = false;
            this.state.currentUser = null;
            this.commentSystem.setCurrentUser(null);
            this.updateUserInterface();
            this.updateAdminButton();
            this.updateFloatingButton();
        });
    }

    /**
     * 绑定评论弹窗事件
     */
    bindCommentModalEvents() {
        // 关闭弹窗
        if (this.elements.commentModalClose) {
            this.elements.commentModalClose.addEventListener('click', () => this.closeCommentModal());
        }
        if (this.elements.commentModalOverlay) {
            this.elements.commentModalOverlay.addEventListener('click', (e) => {
                if (e.target === this.elements.commentModalOverlay) {
                    this.closeCommentModal();
                }
            });
        }

        // 字符计数
        if (this.elements.commentTitle) {
            this.elements.commentTitle.addEventListener('input', () => this.updateCharCount('title'));
        }
        if (this.elements.commentContent) {
            this.elements.commentContent.addEventListener('input', () => {
                this.updateCharCount('content');
                this.saveDraftDebounced();
            });
        }

        // 图片上传
        if (this.elements.uploadImageBtn) {
            this.elements.uploadImageBtn.addEventListener('click', () => this.triggerImageUpload());
        }
        if (this.elements.imageInput) {
            this.elements.imageInput.addEventListener('change', (e) => this.handleImageSelect(e));
        }

        // 颜色选择
        if (this.elements.colorPickerBtn) {
            this.elements.colorPickerBtn.addEventListener('click', () => this.toggleColorPicker());
        }
        if (this.elements.colorPickerClose) {
            this.elements.colorPickerClose.addEventListener('click', () => this.closeColorPicker());
        }

        // 表单提交
        if (this.elements.commentForm) {
            this.elements.commentForm.addEventListener('submit', (e) => this.handleCommentSubmit(e));
        }
        if (this.elements.cancelCommentBtn) {
            this.elements.cancelCommentBtn.addEventListener('click', () => this.closeCommentModal());
        }
    }

    /**
     * 绑定回复弹窗事件
     */
    bindReplyModalEvents() {
        // 关闭弹窗
        if (this.elements.replyModalClose) {
            this.elements.replyModalClose.addEventListener('click', () => this.closeReplyModal());
        }
        if (this.elements.replyModalOverlay) {
            this.elements.replyModalOverlay.addEventListener('click', (e) => {
                if (e.target === this.elements.replyModalOverlay) {
                    this.closeReplyModal();
                }
            });
        }

        // 字符计数
        if (this.elements.replyContent) {
            this.elements.replyContent.addEventListener('input', () => this.updateCharCount('reply'));
        }

        // 表单提交
        if (this.elements.replyForm) {
            this.elements.replyForm.addEventListener('submit', (e) => this.handleReplySubmit(e));
        }
        if (this.elements.cancelReplyBtn) {
            this.elements.cancelReplyBtn.addEventListener('click', () => this.closeReplyModal());
        }
    }

    /**
     * 绑定登录弹窗事件
     */
    bindLoginModalEvents() {
        // 关闭弹窗
        if (this.elements.closeModal) {
            this.elements.closeModal.addEventListener('click', () => this.closeLoginModal());
        }
        if (this.elements.loginModalOverlay) {
            this.elements.loginModalOverlay.addEventListener('click', (e) => {
                if (e.target === this.elements.loginModalOverlay) {
                    this.closeLoginModal();
                }
            });
        }

        // 标签切换
        if (this.elements.loginTab) {
            this.elements.loginTab.addEventListener('click', () => this.switchToLoginTab());
        }
        if (this.elements.registerTab) {
            this.elements.registerTab.addEventListener('click', () => this.switchToRegisterTab());
        }

        // 表单切换链接
        if (this.elements.switchToRegister) {
            this.elements.switchToRegister.addEventListener('click', (e) => {
                e.preventDefault();
                this.switchToRegisterTab();
            });
        }
        if (this.elements.switchToLogin) {
            this.elements.switchToLogin.addEventListener('click', (e) => {
                e.preventDefault();
                this.switchToLoginTab();
            });
        }

        // 登录和注册按钮
        if (this.elements.loginButton) {
            this.elements.loginButton.addEventListener('click', () => this.handleLogin());
        }
        if (this.elements.registerButton) {
            this.elements.registerButton.addEventListener('click', () => this.handleRegister());
        }

        // 个人信息弹窗
        if (this.elements.userAvatar) {
            this.elements.userAvatar.addEventListener('click', () => this.openProfileModal());
        }
        if (this.elements.closeProfileModal) {
            this.elements.closeProfileModal.addEventListener('click', () => this.closeProfileModal());
        }
        if (this.elements.logoutButton) {
            this.elements.logoutButton.addEventListener('click', () => this.handleLogout());
        }
    }

    /**
     * 初始化颜色选择器
     */
    initColorPicker() {
        if (typeof Pickr !== 'undefined' && this.elements.colorPicker) {
            this.colorPicker = Pickr.create({
                el: this.elements.colorPicker,
                theme: 'classic',
                default: '#ffffff',
                swatches: [
                    '#ffffff', '#f8f9fa', '#e9ecef', '#dee2e6',
                    '#ffe8e8', '#fff0e8', '#fff8e8', '#f0ffe8',
                    '#e8f8ff', '#e8f0ff', '#f0e8ff', '#ffe8f8',
                    '#5e35b1', '#7e57c2', '#9c27b0', '#e91e63',
                    '#f44336', '#ff9800', '#4caf50', '#2196f3'
                ],
                components: {
                    preview: true,
                    opacity: false,
                    hue: true,
                    interaction: {
                        hex: true,
                        rgba: false,
                        input: true,
                        save: true
                    }
                }
            });

            // 监听颜色变化事件（实时预览）
            this.colorPicker.on('change', (color) => {
                if (color) {
                    const colorString = color.toHEXA().toString();
                    this.updateColorPreview(colorString);
                }
            });

            // 监听保存事件
            this.colorPicker.on('save', (color) => {
                if (color) {
                    this.state.selectedColor = color.toHEXA().toString();
                    this.updateColorPreview(this.state.selectedColor);
                    this.updateColorButton();
                    this.showToast('颜色已选择', 'success');
                }
                this.closeColorPicker();
            });

            // 监听取消事件
            this.colorPicker.on('cancel', () => {
                this.closeColorPicker();
            });

            // 初始化颜色预览
            this.updateColorPreview(this.state.selectedColor);
            this.updateColorButton();
        }
    }

    /**
     * 处理搜索
     */
    handleSearch() {
        const keyword = this.elements.searchInput?.value || '';
        this.commentSystem.searchComments(keyword);
        this.loadComments();
    }

    /**
     * 处理排序变化
     */
    handleSortChange(sortBy) {
        this.commentSystem.setSortBy(sortBy);
        this.loadComments();
    }

    /**
     * 打开评论弹窗
     */
    openCommentModal() {
        if (!this.state.isLoggedIn) {
            this.showToast('请先登录', 'warning');
            this.openLoginModal();
            return;
        }

        // 检查用户是否被禁言
        if (this.isCurrentUserBanned()) {
            this.showToast('您被管理员禁言，请联系管理员', 'error');
            return;
        }

        if (this.elements.commentModalOverlay) {
            this.elements.commentModalOverlay.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
    }

    /**
     * 关闭评论弹窗
     */
    closeCommentModal() {
        if (this.elements.commentModalOverlay) {
            this.elements.commentModalOverlay.classList.remove('active');
            document.body.style.overflow = '';
        }

        // 清空表单
        this.resetCommentForm();
    }

    /**
     * 打开回复弹窗
     */
    openReplyModal(commentId, commentTitle, commentAuthor) {
        if (!this.state.isLoggedIn) {
            this.showToast('请先登录', 'warning');
            this.openLoginModal();
            return;
        }

        // 检查用户是否被禁言
        if (this.isCurrentUserBanned()) {
            this.showToast('您被管理员禁言，请联系管理员', 'error');
            return;
        }

        this.state.currentReplyTo = commentId;

        // 设置回复信息
        if (this.elements.replyToInfo) {
            this.elements.replyToInfo.innerHTML = `
                <h4>回复评论</h4>
                <p><strong>${commentAuthor}</strong>: ${commentTitle}</p>
            `;
        }

        if (this.elements.replyModalOverlay) {
            this.elements.replyModalOverlay.classList.add('active');
            document.body.style.overflow = 'hidden';
        }

        // 聚焦到回复输入框
        setTimeout(() => {
            if (this.elements.replyContent) {
                this.elements.replyContent.focus();
            }
        }, 300);
    }

    /**
     * 关闭回复弹窗
     */
    closeReplyModal() {
        if (this.elements.replyModalOverlay) {
            this.elements.replyModalOverlay.classList.remove('active');
            document.body.style.overflow = '';
        }

        // 清空表单
        this.resetReplyForm();
        this.state.currentReplyTo = null;
    }

    /**
     * 打开登录弹窗
     */
    openLoginModal() {
        if (this.elements.loginModalOverlay) {
            this.elements.loginModalOverlay.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
    }

    /**
     * 关闭登录弹窗
     */
    closeLoginModal() {
        if (this.elements.loginModalOverlay) {
            this.elements.loginModalOverlay.classList.remove('active');
            document.body.style.overflow = '';
        }
    }

    /**
     * 切换到登录标签
     */
    switchToLoginTab() {
        if (this.elements.loginTab && this.elements.registerTab) {
            this.elements.loginTab.classList.add('active');
            this.elements.registerTab.classList.remove('active');
        }
        if (this.elements.loginForm && this.elements.registerForm) {
            this.elements.loginForm.classList.add('active');
            this.elements.registerForm.classList.remove('active');
        }
    }

    /**
     * 切换到注册标签
     */
    switchToRegisterTab() {
        if (this.elements.loginTab && this.elements.registerTab) {
            this.elements.loginTab.classList.remove('active');
            this.elements.registerTab.classList.add('active');
        }
        if (this.elements.loginForm && this.elements.registerForm) {
            this.elements.loginForm.classList.remove('active');
            this.elements.registerForm.classList.add('active');
        }
    }

    /**
     * 处理登录
     */
    handleLogin() {
        const username = document.getElementById('loginUsername')?.value.trim();
        const password = document.getElementById('loginPassword')?.value.trim();

        if (!username || !password) {
            this.showNotification('请输入用户名和密码', 'warning');
            return;
        }

        // 获取用户数据（与主页保持一致）
        let users = [
            {
                username: 'admin',
                password: 'admin123',
                uid: 'UID_123456',
                avatar: '../images/avatar.png'
            },
            {
                username: 'test',
                password: 'test123',
                uid: 'UID_654321',
                avatar: '../images/avatar.png'
            }
        ];

        // 尝试从localStorage获取注册用户
        try {
            const registeredUsers = localStorage.getItem('registeredUsers');
            if (registeredUsers) {
                const additionalUsers = JSON.parse(registeredUsers);
                users = users.concat(additionalUsers);
            }
        } catch (error) {
            console.error('读取注册用户失败:', error);
        }

        const user = users.find(u => u.username === username && u.password === password);

        if (user) {
            this.state.isLoggedIn = true;
            this.state.currentUser = user;
            this.commentSystem.setCurrentUser(user);

            // 保存登录状态到localStorage
            localStorage.setItem('currentUser', JSON.stringify(user));

            this.updateUserInterface();
            this.closeLoginModal();
            this.showNotification('登录成功！', 'success');

            // 触发登录事件
            window.dispatchEvent(new CustomEvent('userLoggedIn', {
                detail: { user: user }
            }));
        } else {
            this.showNotification('用户名或密码错误', 'error');
        }
    }

    /**
     * 处理注册
     */
    handleRegister() {
        const username = document.getElementById('registerUsername')?.value.trim();
        const password = document.getElementById('registerPassword')?.value.trim();
        const confirmPassword = document.getElementById('confirmPassword')?.value.trim();

        if (!username || !password || !confirmPassword) {
            this.showNotification('请填写完整信息', 'warning');
            return;
        }

        if (password !== confirmPassword) {
            this.showNotification('两次输入的密码不一致', 'warning');
            return;
        }

        if (password.length < 6) {
            this.showNotification('密码长度至少6位', 'warning');
            return;
        }

        // 生成新用户
        const newUser = {
            username: username,
            password: password,
            uid: 'UID_' + Date.now(),
            avatar: '../images/avatar.png'
        };

        // 这里应该保存到数据库，现在先保存到localStorage
        let users = JSON.parse(localStorage.getItem('registeredUsers') || '[]');

        // 检查用户名是否已存在
        if (users.some(u => u.username === username)) {
            this.showNotification('用户名已存在', 'error');
            return;
        }

        users.push(newUser);
        localStorage.setItem('registeredUsers', JSON.stringify(users));

        this.showNotification('注册成功！请登录', 'success');
        this.switchToLoginTab();

        // 自动填入登录信息
        setTimeout(() => {
            const loginUsernameInput = document.getElementById('loginUsername');
            const loginPasswordInput = document.getElementById('loginPassword');
            if (loginUsernameInput) loginUsernameInput.value = username;
            if (loginPasswordInput) loginPasswordInput.value = password;
        }, 500);
    }

    /**
     * 打开个人信息弹窗
     */
    openProfileModal() {
        if (!this.state.isLoggedIn) {
            this.openLoginModal();
            return;
        }

        if (this.elements.profileModalOverlay) {
            this.elements.profileModalOverlay.classList.add('active');
            document.body.style.overflow = 'hidden';

            // 填入当前用户信息
            const profileUsername = document.getElementById('profileUsername');
            const userUniqueId = document.getElementById('userUniqueId');

            if (profileUsername) profileUsername.value = this.state.currentUser.username;
            if (userUniqueId) userUniqueId.textContent = this.state.currentUser.uid;
        }
    }

    /**
     * 关闭个人信息弹窗
     */
    closeProfileModal() {
        if (this.elements.profileModalOverlay) {
            this.elements.profileModalOverlay.classList.remove('active');
            document.body.style.overflow = '';
        }
    }

    /**
     * 处理退出登录
     */
    handleLogout() {
        this.state.isLoggedIn = false;
        this.state.currentUser = null;
        this.commentSystem.setCurrentUser(null);

        // 清除登录状态
        localStorage.removeItem('currentUser');

        this.updateUserInterface();
        this.closeProfileModal();
        this.showNotification('已退出登录', 'info');

        // 触发退出登录事件
        window.dispatchEvent(new CustomEvent('userLoggedOut'));
    }

    /**
     * 更新字符计数
     */
    updateCharCount(type) {
        let input, counter, maxLength;

        switch (type) {
            case 'title':
                input = this.elements.commentTitle;
                counter = this.elements.titleCharCount;
                maxLength = 100;
                break;
            case 'content':
                input = this.elements.commentContent;
                counter = this.elements.contentCharCount;
                maxLength = 1000;
                break;
            case 'reply':
                input = this.elements.replyContent;
                counter = this.elements.replyCharCount;
                maxLength = 500;
                break;
            default:
                return;
        }

        if (input && counter) {
            const currentLength = input.value.length;
            counter.textContent = currentLength;

            // 根据字符数量改变颜色
            if (currentLength > maxLength * 0.9) {
                counter.style.color = '#e74c3c';
            } else if (currentLength > maxLength * 0.7) {
                counter.style.color = '#f39c12';
            } else {
                counter.style.color = '#666';
            }
        }
    }

    /**
     * 显示Toast提示消息（简洁版）
     */
    showToast(message, type = 'info') {
        // 移除已存在的toast
        const existingToast = document.querySelector('.toast-notification');
        if (existingToast) {
            existingToast.remove();
        }

        // 创建新的toast
        const toast = document.createElement('div');
        toast.className = `toast-notification ${type}`;
        toast.textContent = message;

        document.body.appendChild(toast);

        // 显示动画
        setTimeout(() => {
            toast.classList.add('show');
        }, 100);

        // 自动隐藏
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 2000);
    }

    /**
     * 显示通知消息（详细版，保留原有功能）
     */
    showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;

        // 添加样式
        notification.style.cssText = `
            position: fixed;
            top: 100px;
            right: 20px;
            background: ${this.getNotificationColor(type)};
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            z-index: 3000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            max-width: 300px;
        `;

        document.body.appendChild(notification);

        // 显示动画
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // 自动隐藏
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    /**
     * 获取通知图标
     */
    getNotificationIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    /**
     * 获取通知颜色
     */
    getNotificationColor(type) {
        const colors = {
            success: '#27ae60',
            error: '#e74c3c',
            warning: '#f39c12',
            info: '#3498db'
        };
        return colors[type] || '#3498db';
    }

    /**
     * 触发图片上传
     */
    triggerImageUpload() {
        if (this.elements.imageInput) {
            this.elements.imageInput.click();
        }
    }

    /**
     * 处理图片选择
     */
    async handleImageSelect(event) {
        const files = Array.from(event.target.files);
        if (files.length === 0) return;

        // 检查文件数量限制
        const totalImages = this.state.selectedImages.length + files.length;
        if (totalImages > 9) {
            this.showNotification('最多只能上传9张图片', 'warning');
            return;
        }

        this.state.isUploading = true;
        this.updateUploadButton();

        try {
            // 批量上传图片
            const results = await this.imageUploader.uploadFiles(
                files,
                (progress, status) => {
                    this.updateUploadProgress(progress, status);
                },
                (result, index, total) => {
                    if (result.success) {
                        this.state.selectedImages.push(result);
                        this.updateImagePreview();
                    } else {
                        this.showNotification(`图片 ${result.filename} 上传失败: ${result.error}`, 'error');
                    }
                }
            );

            this.showNotification(`成功上传 ${results.filter(r => r.success).length} 张图片`, 'success');

        } catch (error) {
            this.showNotification('图片上传失败: ' + error.message, 'error');
        } finally {
            this.state.isUploading = false;
            this.updateUploadButton();
            // 清空文件输入
            if (this.elements.imageInput) {
                this.elements.imageInput.value = '';
            }
        }
    }

    /**
     * 更新上传按钮状态
     */
    updateUploadButton() {
        if (this.elements.uploadImageBtn) {
            const btn = this.elements.uploadImageBtn;
            if (this.state.isUploading) {
                btn.disabled = true;
                btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span>上传中...</span>';
            } else {
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-image"></i><span>添加图片</span>';
            }
        }
    }

    /**
     * 更新上传进度
     */
    updateUploadProgress(progress, status) {
        if (this.elements.uploadImageBtn) {
            this.elements.uploadImageBtn.innerHTML = `
                <i class="fas fa-spinner fa-spin"></i>
                <span>${status} (${Math.round(progress)}%)</span>
            `;
        }
    }

    /**
     * 更新图片预览
     */
    updateImagePreview() {
        if (!this.elements.imagePreviewContainer || !this.elements.imagePreviewList) return;

        if (this.state.selectedImages.length > 0) {
            this.elements.imagePreviewContainer.style.display = 'block';

            this.elements.imagePreviewList.innerHTML = this.state.selectedImages.map((image, index) => `
                <div class="image-preview-item" data-index="${index}">
                    <img src="${image.url}" alt="${image.filename}" class="preview-image">
                    <button type="button" class="remove-image-btn" onclick="schoolReviewApp.removeImage(${index})">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `).join('');
        } else {
            this.elements.imagePreviewContainer.style.display = 'none';
        }
    }

    /**
     * 移除图片
     */
    removeImage(index) {
        this.state.selectedImages.splice(index, 1);
        this.updateImagePreview();
    }

    /**
     * 切换颜色选择器
     */
    toggleColorPicker() {
        if (this.elements.colorPickerContainer) {
            const isVisible = this.elements.colorPickerContainer.style.display === 'block';

            if (isVisible) {
                this.closeColorPicker();
            } else {
                this.openColorPicker();
            }
        }
    }

    /**
     * 打开颜色选择器
     */
    openColorPicker() {
        if (this.elements.colorPickerContainer) {
            this.elements.colorPickerContainer.style.display = 'block';
            document.body.style.overflow = 'hidden';

            if (this.colorPicker) {
                this.colorPicker.show();
            }
        }
    }

    /**
     * 关闭颜色选择器
     */
    closeColorPicker() {
        if (this.elements.colorPickerContainer) {
            this.elements.colorPickerContainer.style.display = 'none';
            document.body.style.overflow = '';

            if (this.colorPicker) {
                this.colorPicker.hide();
            }
        }
    }

    /**
     * 更新颜色预览
     * @param {string} color - 颜色值，如果不提供则使用当前选中的颜色
     */
    updateColorPreview(color = null) {
        const previewColor = color || this.state.selectedColor;

        // 更新预览框的颜色
        if (this.elements.colorPreviewBox) {
            this.elements.colorPreviewBox.style.background = previewColor;

            // 根据颜色亮度调整文字颜色
            const textColor = this.getContrastColor(previewColor);
            const textElement = this.elements.colorPreviewBox.querySelector('.color-preview-text');
            if (textElement) {
                textElement.style.color = textColor;
                textElement.style.textShadow = textColor === '#000000' ?
                    '1px 1px 2px rgba(255, 255, 255, 0.8)' :
                    '1px 1px 2px rgba(0, 0, 0, 0.8)';
            }
        }
    }

    /**
     * 更新颜色按钮样式
     */
    updateColorButton() {
        if (this.elements.colorPickerBtn) {
            const color = this.state.selectedColor;
            this.elements.colorPickerBtn.style.background = `linear-gradient(45deg, ${color}, ${color}dd)`;
            this.elements.colorPickerBtn.style.borderColor = color;
        }
    }

    /**
     * 获取对比色（用于文字显示）
     * @param {string} hexColor - 十六进制颜色值
     * @returns {string} 黑色或白色
     */
    getContrastColor(hexColor) {
        // 移除#号
        const hex = hexColor.replace('#', '');

        // 转换为RGB
        const r = parseInt(hex.substr(0, 2), 16);
        const g = parseInt(hex.substr(2, 2), 16);
        const b = parseInt(hex.substr(4, 2), 16);

        // 计算亮度
        const brightness = (r * 299 + g * 587 + b * 114) / 1000;

        // 返回对比色
        return brightness > 128 ? '#000000' : '#ffffff';
    }

    /**
     * 判断颜色是否为浅色
     * @param {string} hexColor - 十六进制颜色值
     * @returns {boolean} 是否为浅色
     */
    isLightColor(hexColor) {
        const hex = hexColor.replace('#', '');
        const r = parseInt(hex.substr(0, 2), 16);
        const g = parseInt(hex.substr(2, 2), 16);
        const b = parseInt(hex.substr(4, 2), 16);
        const brightness = (r * 299 + g * 587 + b * 114) / 1000;
        return brightness > 128;
    }

    /**
     * 获取更深的颜色（用于边框）
     * @param {string} hexColor - 十六进制颜色值
     * @returns {string} 更深的颜色
     */
    getDarkerColor(hexColor) {
        const hex = hexColor.replace('#', '');
        let r = parseInt(hex.substr(0, 2), 16);
        let g = parseInt(hex.substr(2, 2), 16);
        let b = parseInt(hex.substr(4, 2), 16);

        // 减少亮度（乘以0.8）
        r = Math.floor(r * 0.8);
        g = Math.floor(g * 0.8);
        b = Math.floor(b * 0.8);

        // 转换回十六进制
        const toHex = (n) => {
            const hex = n.toString(16);
            return hex.length === 1 ? '0' + hex : hex;
        };

        return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
    }

    /**
     * 处理评论提交
     */
    async handleCommentSubmit(event) {
        event.preventDefault();

        if (!this.state.isLoggedIn) {
            this.showNotification('请先登录', 'warning');
            return;
        }

        const title = this.elements.commentTitle?.value.trim();
        const content = this.elements.commentContent?.value.trim();

        if (!title || !content) {
            this.showNotification('标题和内容不能为空', 'warning');
            return;
        }

        // 禁用提交按钮
        if (this.elements.submitCommentBtn) {
            this.elements.submitCommentBtn.disabled = true;
            this.elements.submitCommentBtn.textContent = '发表中...';
        }

        try {
            const commentData = {
                title: title,
                content: content,
                images: this.state.selectedImages,
                backgroundColor: this.state.selectedColor
            };

            const result = this.commentSystem.addComment(commentData);

            if (result.success) {
                this.showNotification('评论发表成功！', 'success');
                this.closeCommentModal();
                this.loadComments(); // 重新加载评论列表
            } else {
                this.showNotification(result.error, 'error');
            }
        } catch (error) {
            this.showNotification('发表评论失败: ' + error.message, 'error');
        } finally {
            // 恢复提交按钮
            if (this.elements.submitCommentBtn) {
                this.elements.submitCommentBtn.disabled = false;
                this.elements.submitCommentBtn.textContent = '发表评论';
            }
        }
    }

    /**
     * 处理回复提交
     */
    async handleReplySubmit(event) {
        event.preventDefault();

        if (!this.state.isLoggedIn || !this.state.currentReplyTo) {
            this.showNotification('回复失败', 'error');
            return;
        }

        const content = this.elements.replyContent?.value.trim();

        if (!content) {
            this.showNotification('回复内容不能为空', 'warning');
            return;
        }

        // 禁用提交按钮
        if (this.elements.submitReplyBtn) {
            this.elements.submitReplyBtn.disabled = true;
            this.elements.submitReplyBtn.textContent = '回复中...';
        }

        try {
            const result = this.commentSystem.addReply(this.state.currentReplyTo, content);

            if (result.success) {
                this.showNotification('回复发表成功！', 'success');
                this.closeReplyModal();
                this.loadComments(); // 重新加载评论列表
            } else {
                this.showNotification(result.error, 'error');
            }
        } catch (error) {
            this.showNotification('发表回复失败: ' + error.message, 'error');
        } finally {
            // 恢复提交按钮
            if (this.elements.submitReplyBtn) {
                this.elements.submitReplyBtn.disabled = false;
                this.elements.submitReplyBtn.textContent = '发表回复';
            }
        }
    }

    /**
     * 重置评论表单
     */
    resetCommentForm() {
        if (this.elements.commentTitle) this.elements.commentTitle.value = '';
        if (this.elements.commentContent) this.elements.commentContent.value = '';

        this.state.selectedImages = [];
        this.state.selectedColor = '#ffffff';

        this.updateImagePreview();
        this.updateColorPreview();
        this.updateColorButton();
        this.updateCharCount('title');
        this.updateCharCount('content');

        this.closeColorPicker();
    }

    /**
     * 重置回复表单
     */
    resetReplyForm() {
        if (this.elements.replyContent) this.elements.replyContent.value = '';
        this.updateCharCount('reply');
    }

    /**
     * 加载评论列表
     */
    loadComments() {
        if (this.elements.loadingIndicator) {
            this.elements.loadingIndicator.style.display = 'block';
        }

        // 重置分页
        this.commentSystem.currentPage = 1;

        setTimeout(() => {
            const result = this.commentSystem.getPagedComments(1);
            this.renderComments(result.comments, true);
            this.updateLoadMoreButton(result.hasMore);

            if (this.elements.loadingIndicator) {
                this.elements.loadingIndicator.style.display = 'none';
            }
        }, 500); // 模拟加载延迟
    }

    /**
     * 加载更多评论
     */
    loadMoreComments() {
        if (this.elements.loadMoreBtn) {
            this.elements.loadMoreBtn.disabled = true;
            this.elements.loadMoreBtn.textContent = '加载中...';
        }

        setTimeout(() => {
            const result = this.commentSystem.loadMoreComments();
            this.renderComments(result.comments, false);
            this.updateLoadMoreButton(result.hasMore);

            if (this.elements.loadMoreBtn) {
                this.elements.loadMoreBtn.disabled = false;
                this.elements.loadMoreBtn.textContent = '加载更多评论';
            }
        }, 500);
    }

    /**
     * 渲染评论列表
     */
    renderComments(comments, replace = true) {
        if (!this.elements.commentsContainer) return;

        const commentsHtml = comments.map(comment => this.renderComment(comment)).join('');

        if (replace) {
            this.elements.commentsContainer.innerHTML = commentsHtml;
        } else {
            this.elements.commentsContainer.insertAdjacentHTML('beforeend', commentsHtml);
        }

        // 绑定评论事件
        this.bindCommentEvents();
    }

    /**
     * 渲染单个评论
     */
    renderComment(comment) {
        const repliesHtml = comment.replies.map(reply => this.renderReply(reply)).join('');
        const hasReplies = comment.replies.length > 0;

        // 计算文字颜色（根据背景色亮度）
        const textColor = this.getContrastColor(comment.backgroundColor || '#ffffff');
        const isLightBackground = this.isLightColor(comment.backgroundColor || '#ffffff');

        return `
            <div class="comment-card" data-comment-id="${comment.id}"
                 style="background-color: ${comment.backgroundColor || '#ffffff'};
                        border-left-color: ${this.getDarkerColor(comment.backgroundColor || '#ffffff')};">
                <div class="comment-header">
                    <div class="comment-user-info">
                        <img src="${comment.author.avatar}" alt="${comment.author.username}" class="comment-avatar">
                        <div class="comment-user-details">
                            <h4 style="color: ${textColor};">
                                ${comment.author.username}
                                ${comment.author.title ? `<span class="user-title" style="background: ${comment.author.title.color}">${comment.author.title.title}</span>` : ''}
                            </h4>
                            <div class="comment-time" style="color: ${isLightBackground ? '#666' : '#ccc'};">${this.commentSystem.formatTime(comment.createTime)}</div>
                        </div>
                    </div>
                    <div class="comment-actions">
                        ${this.state.currentUser && this.state.currentUser.uid === comment.author.uid ?
                            `<button class="action-btn-small delete-comment-btn" data-comment-id="${comment.id}"
                                     style="color: ${isLightBackground ? '#666' : '#ccc'};">
                                <i class="fas fa-trash"></i>
                            </button>` : ''
                        }
                    </div>
                </div>

                <div class="comment-content">
                    <h3 class="comment-title" style="color: ${textColor};">${comment.title}</h3>
                    <p class="comment-text" style="color: ${textColor};">${comment.content}</p>

                    ${comment.images.length > 0 ? `
                        <div class="comment-images">
                            <div class="image-grid">
                                ${comment.images.map(image => `
                                    <img src="${image.url}" alt="${image.filename}" class="comment-image" onclick="schoolReviewApp.openImageModal('${image.url}')">
                                `).join('')}
                            </div>
                        </div>
                    ` : ''}
                </div>

                <div class="comment-footer" style="border-top-color: ${isLightBackground ? 'var(--accent-color)' : 'rgba(255,255,255,0.2)'};">
                    <div class="comment-stats">
                        <button class="stat-btn like-btn ${comment.userReaction === 'like' ? 'liked' : ''}"
                                data-comment-id="${comment.id}" data-action="like"
                                style="color: ${comment.userReaction === 'like' ? '#e74c3c' : (isLightBackground ? '#666' : '#ccc')};">
                            <i class="fas fa-thumbs-up"></i>
                            <span>${comment.likes}</span>
                        </button>
                        <button class="stat-btn dislike-btn ${comment.userReaction === 'dislike' ? 'disliked' : ''}"
                                data-comment-id="${comment.id}" data-action="dislike"
                                style="color: ${comment.userReaction === 'dislike' ? '#95a5a6' : (isLightBackground ? '#666' : '#ccc')};">
                            <i class="fas fa-thumbs-down"></i>
                            <span>${comment.dislikes}</span>
                        </button>
                    </div>
                    <button class="comment-reply-btn" data-comment-id="${comment.id}"
                            data-comment-title="${comment.title}" data-comment-author="${comment.author.username}"
                            style="background: ${isLightBackground ? 'var(--primary-color)' : 'rgba(255,255,255,0.9)'};
                                   color: ${isLightBackground ? 'var(--secondary-color)' : 'var(--primary-color)'};">
                        <i class="fas fa-reply"></i>
                        回复
                    </button>
                </div>

                ${hasReplies ? `
                    <div class="replies-container" style="border-left-color: ${isLightBackground ? 'var(--accent-color)' : 'rgba(255,255,255,0.3)'};">
                        ${repliesHtml}
                    </div>
                ` : ''}
            </div>
        `;
    }

    /**
     * 渲染回复
     */
    renderReply(reply) {
        return `
            <div class="reply-item" data-reply-id="${reply.id}">
                <div class="reply-header">
                    <span class="reply-user">
                        ${reply.author.username}
                        ${reply.author.title ? `<span class="user-title" style="background: ${reply.author.title.color}">${reply.author.title.title}</span>` : ''}
                    </span>
                    <span class="reply-time">${this.commentSystem.formatTime(reply.createTime)}</span>
                </div>
                <div class="reply-content">${reply.content}</div>
            </div>
        `;
    }

    /**
     * 绑定评论事件
     */
    bindCommentEvents() {
        // 点赞/踩按钮
        document.querySelectorAll('.like-btn, .dislike-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const commentId = e.currentTarget.dataset.commentId;
                const action = e.currentTarget.dataset.action;
                this.handleCommentReaction(commentId, action);
            });
        });

        // 回复按钮
        document.querySelectorAll('.comment-reply-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const commentId = e.currentTarget.dataset.commentId;
                const commentTitle = e.currentTarget.dataset.commentTitle;
                const commentAuthor = e.currentTarget.dataset.commentAuthor;
                this.openReplyModal(commentId, commentTitle, commentAuthor);
            });
        });

        // 删除按钮
        document.querySelectorAll('.delete-comment-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const commentId = e.currentTarget.dataset.commentId;
                this.handleDeleteComment(commentId);
            });
        });
    }

    /**
     * 处理评论反应（点赞/踩）
     */
    handleCommentReaction(commentId, action) {
        if (!this.state.isLoggedIn) {
            this.showToast('请先登录', 'warning');
            return;
        }

        const result = this.commentSystem.reactToComment(commentId, action);

        if (result.success) {
            // 更新按钮状态
            this.updateReactionButtons(commentId, result.comment);
        } else {
            this.showNotification(result.error, 'error');
        }
    }

    /**
     * 更新反应按钮状态
     */
    updateReactionButtons(commentId, comment) {
        const likeBtn = document.querySelector(`.like-btn[data-comment-id="${commentId}"]`);
        const dislikeBtn = document.querySelector(`.dislike-btn[data-comment-id="${commentId}"]`);

        if (likeBtn) {
            likeBtn.className = `stat-btn like-btn ${comment.userReaction === 'like' ? 'liked' : ''}`;
            likeBtn.querySelector('span').textContent = comment.likes;
        }

        if (dislikeBtn) {
            dislikeBtn.className = `stat-btn dislike-btn ${comment.userReaction === 'dislike' ? 'disliked' : ''}`;
            dislikeBtn.querySelector('span').textContent = comment.dislikes;
        }
    }

    /**
     * 处理删除评论
     */
    handleDeleteComment(commentId) {
        if (!confirm('确定要删除这条评论吗？删除后无法恢复。')) {
            return;
        }

        const result = this.commentSystem.deleteComment(commentId);

        if (result.success) {
            this.showNotification('评论已删除', 'success');
            this.loadComments(); // 重新加载评论列表
        } else {
            this.showNotification(result.error, 'error');
        }
    }

    /**
     * 更新加载更多按钮
     */
    updateLoadMoreButton(hasMore) {
        if (this.elements.loadMoreContainer) {
            this.elements.loadMoreContainer.style.display = hasMore ? 'block' : 'none';
        }
    }

    /**
     * 打开图片模态框
     */
    openImageModal(imageUrl) {
        // 创建图片查看模态框
        const modal = document.createElement('div');
        modal.className = 'image-modal-overlay';
        modal.innerHTML = `
            <div class="image-modal">
                <button class="image-modal-close">&times;</button>
                <img src="${imageUrl}" alt="查看图片" class="modal-image">
            </div>
        `;

        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 3000;
            cursor: pointer;
        `;

        const modalContent = modal.querySelector('.image-modal');
        modalContent.style.cssText = `
            position: relative;
            max-width: 90%;
            max-height: 90%;
            cursor: default;
        `;

        const closeBtn = modal.querySelector('.image-modal-close');
        closeBtn.style.cssText = `
            position: absolute;
            top: -40px;
            right: 0;
            background: none;
            border: none;
            color: white;
            font-size: 30px;
            cursor: pointer;
            z-index: 3001;
        `;

        const image = modal.querySelector('.modal-image');
        image.style.cssText = `
            max-width: 100%;
            max-height: 100%;
            border-radius: 8px;
        `;

        // 关闭事件
        modal.addEventListener('click', () => {
            document.body.removeChild(modal);
            document.body.style.overflow = '';
        });

        modalContent.addEventListener('click', (e) => {
            e.stopPropagation();
        });

        closeBtn.addEventListener('click', () => {
            document.body.removeChild(modal);
            document.body.style.overflow = '';
        });

        document.body.appendChild(modal);
        document.body.style.overflow = 'hidden';
    }

    /**
     * 保存草稿（防抖）
     */
    saveDraftDebounced = debounce(() => {
        const title = this.elements.commentTitle?.value || '';
        const content = this.elements.commentContent?.value || '';

        if (title || content) {
            this.commentSystem.saveDraft({
                title: title,
                content: content,
                images: this.state.selectedImages,
                backgroundColor: this.state.selectedColor
            });
        }
    }, 1000);

    /**
     * 加载草稿
     */
    loadDraft() {
        const draft = this.commentSystem.loadDraft();
        if (draft) {
            // 询问是否恢复草稿
            if (confirm('检测到未完成的草稿，是否恢复？')) {
                if (this.elements.commentTitle) this.elements.commentTitle.value = draft.title || '';
                if (this.elements.commentContent) this.elements.commentContent.value = draft.content || '';

                this.state.selectedImages = draft.images || [];
                this.state.selectedColor = draft.backgroundColor || '#ffffff';

                this.updateImagePreview();
                this.updateColorPreview();
                this.updateCharCount('title');
                this.updateCharCount('content');
            } else {
                this.commentSystem.clearDraft();
            }
        }
    }

    /**
     * 检查当前用户是否被禁言
     */
    isCurrentUserBanned() {
        if (!this.state.currentUser) return false;

        // 如果有全局的isUserBanned函数，使用它
        if (typeof isUserBanned === 'function') {
            return isUserBanned(this.state.currentUser.uid);
        }

        // 否则直接检查用户属性
        return this.state.currentUser.isBanned || false;
    }

    /**
     * 获取用户头衔信息
     */
    getUserTitleInfo(uid) {
        // 如果有全局的getUserTitle函数，使用它
        if (typeof getUserTitle === 'function') {
            return getUserTitle(uid);
        }

        // 否则从用户数据中查找
        if (typeof users !== 'undefined') {
            const user = users.find(u => u.uid === uid);
            if (user && user.title) {
                return {
                    title: user.title,
                    color: user.titleColor || '#6c5ce7'
                };
            }
        }

        return null;
    }

    /**
     * 更新管理员按钮显示状态
     */
    updateAdminButton() {
        const adminBtn = document.getElementById('adminBtn');
        if (!adminBtn) return;

        // 只有管理员才显示后台管理按钮
        if (this.state.currentUser && this.state.currentUser.role === 'admin') {
            adminBtn.style.display = 'block';
        } else {
            adminBtn.style.display = 'none';
        }
    }

    /**
     * 更新悬浮按钮状态
     */
    updateFloatingButton() {
        const floatingBtn = this.elements.floatingAddBtn;
        if (!floatingBtn) return;

        // 检查用户是否被禁言
        if (this.state.currentUser && this.isCurrentUserBanned()) {
            floatingBtn.classList.add('banned');
        } else {
            floatingBtn.classList.remove('banned');
        }
    }
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 全局变量
let schoolReviewApp;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    schoolReviewApp = new SchoolReviewApp();
});
